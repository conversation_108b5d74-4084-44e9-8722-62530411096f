import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../../../../core/helpers/validators.dart';
import '../providers/auth_provider.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_text_field.dart';

class ResetPasswordScreen extends StatefulWidget {
  const ResetPasswordScreen({super.key});

  @override
  State<ResetPasswordScreen> createState() => _ResetPasswordScreenState();
}

class _ResetPasswordScreenState extends State<ResetPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _otpController = TextEditingController();
  final _newPasswordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();

  @override
  void dispose() {
    _otpController.dispose();
    _newPasswordController.dispose();
    _confirmPasswordController.dispose();
    super.dispose();
  }

  void _resetPassword() async {
    if (!_formKey.currentState!.validate()) return;

    final authProvider = context.read<AuthProvider>();
    
    // Get phone number from temp storage (from forgot password flow)
    final phoneNumber = authProvider.tempPhoneNumber;
    if (phoneNumber == null) {
      _showSnackBar('Session expired. Please start again.', isError: true);
      Navigator.pushNamedAndRemoveUntil(context, '/login', (route) => false);
      return;
    }

    final success = await authProvider.resetPassword(
      phoneNumber,
      _newPasswordController.text,
      _otpController.text.trim(),
    );

    if (success) {
      if (mounted) {
        _showSuccessDialog();
      }
    } else if (authProvider.errorMessage != null) {
      _showSnackBar(authProvider.errorMessage!, isError: true);
    }
  }

  void _showSnackBar(String message, {required bool isError}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.error : AppColors.success,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  void _showSuccessDialog() {
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Container(
              width: 80,
              height: 80,
              decoration: BoxDecoration(
                color: AppColors.success.withOpacity(0.1),
                shape: BoxShape.circle,
              ),
              child: const Icon(
                Icons.check_circle,
                size: 50,
                color: AppColors.success,
              ),
            ),
            const SizedBox(height: 16),
            Text(
              'Password Reset Successful!',
              style: AppTextStyles.headlineSmall.copyWith(
                fontWeight: FontWeight.bold,
                color: AppColors.textPrimary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Your password has been successfully reset. You can now login with your new password.',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            AuthButton(
              text: 'Continue to Login',
              onPressed: () {
                Navigator.pushNamedAndRemoveUntil(
                  context,
                  '/login',
                  (route) => false,
                );
              },
              width: double.infinity,
            ),
          ],
        ),
      ),
    );
  }

  String? _validateOtp(String? value) {
    return Validators.otp(value);
  }

  @override
  Widget build(BuildContext context) {
    final authProvider = context.watch<AuthProvider>();
    
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                _buildHeader(authProvider.tempPhoneNumber),
                const SizedBox(height: 48),

                // Form Fields
                _buildFormFields(),
                const SizedBox(height: 32),

                // Reset Password Button
                _buildResetPasswordButton(),
                const SizedBox(height: 24),

                // Resend Code Link
                _buildResendCodeLink(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(String? phoneNumber) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppColors.primary.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.lock_reset,
            size: 40,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 24),

        // Title
        Text(
          'Reset Password',
          style: AppTextStyles.headlineLarge.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),

        // Subtitle
        RichText(
          text: TextSpan(
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.textSecondary,
            ),
            children: [
              const TextSpan(text: 'Enter the verification code sent to '),
              TextSpan(
                text: phoneNumber ?? 'your phone',
                style: AppTextStyles.bodyLarge.copyWith(
                  color: AppColors.primary,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const TextSpan(text: ' and create a new password.'),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        // OTP Code
        OtpTextField(
          controller: _otpController,
          validator: _validateOtp,
        ),
        const SizedBox(height: 20),

        // New Password
        PasswordTextField(
          label: 'New Password',
          hint: 'Enter your new password',
          controller: _newPasswordController,
          validator: Validators.password,
        ),
        const SizedBox(height: 20),

        // Confirm New Password
        PasswordTextField(
          label: 'Confirm New Password',
          hint: 'Confirm your new password',
          controller: _confirmPasswordController,
          validator: (value) => Validators.confirmPassword(
            value,
            _newPasswordController.text,
          ),
        ),
      ],
    );
  }

  Widget _buildResetPasswordButton() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return AuthButton(
          text: 'Reset Password',
          onPressed: _resetPassword,
          isLoading: authProvider.isLoading,
          width: double.infinity,
          icon: Icons.security,
        );
      },
    );
  }

  Widget _buildResendCodeLink() {
    return Center(
      child: Column(
        children: [
          Text(
            "Didn't receive the code?",
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
          ),
          const SizedBox(height: 8),
          TextLinkButton(
            text: 'Resend Code',
            onPressed: () async {
              final authProvider = context.read<AuthProvider>();
              final success = await authProvider.resendOtp();
              
              if (success && authProvider.successMessage != null) {
                _showSnackBar(authProvider.successMessage!, isError: false);
              } else if (authProvider.errorMessage != null) {
                _showSnackBar(authProvider.errorMessage!, isError: true);
              }
            },
          ),
        ],
      ),
    );
  }
}
