import 'user_model.dart';

class AuthResponse {
  final bool success;
  final String? message;
  final AuthData? data;
  final String? error;

  const AuthResponse({
    required this.success,
    this.message,
    this.data,
    this.error,
  });

  //factory method 
  factory AuthResponse.fromJson(Map<String, dynamic> json) {
    return AuthResponse(
      success: json['success'] ?? false,
      message: json['message'],
      data: json['data'] != null ? AuthData.fromJson(json['data']) : null,
      error: json['error'],
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data?.toJson(),
      'error': error,
    };
  }

  @override
  String toString() {
    return 'AuthResponse(success: $success, message: $message, data: $data, error: $error)';
  }
}

class AuthData {
  final String? accessToken;
  final String? refreshToken;
  final UserModel? user;
  final int? expiresIn;
  final String? tokenType;

  const AuthData({
    this.accessToken,
    this.refreshToken,
    this.user,
    this.expiresIn,
    this.tokenType = 'Bearer',
  });

  factory AuthData.fromJson(Map<String, dynamic> json) {
    return AuthData(
      accessToken: json['access_token'],
      refreshToken: json['refresh_token'],
      user: json['user'] != null ? UserModel.fromJson(json['user']) : null,
      expiresIn: json['expires_in'],
      tokenType: json['token_type'] ?? 'Bearer',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'access_token': accessToken,
      'refresh_token': refreshToken,
      'user': user?.toJson(),
      'expires_in': expiresIn,
      'token_type': tokenType,
    };
  }

  @override
  String toString() {
    return 'AuthData(accessToken: $accessToken, refreshToken: $refreshToken, user: $user, expiresIn: $expiresIn, tokenType: $tokenType)';
  }
}
