import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../../data/models/category_model.dart';
import '../../data/models/hotel_model.dart';
import '../../data/services/home_data_service.dart';
import '../widgets/search_bar_widget.dart';
import '../widgets/category_chip_widget.dart';
import '../widgets/hotel_card_widget.dart';
import '../widgets/bottom_navigation_widget.dart';

class CustomerHomeScreen extends StatefulWidget {
  const CustomerHomeScreen({super.key});

  @override
  State<CustomerHomeScreen> createState() => _CustomerHomeScreenState();
}

class _CustomerHomeScreenState extends State<CustomerHomeScreen> {
  final TextEditingController _searchController = TextEditingController();
  List<CategoryModel> _categories = [];
  List<HotelModel> _hotels = [];
  List<HotelModel> _filteredHotels = [];
  int _currentNavIndex = 0;
  String _selectedCategory = 'Mountain';

  @override
  void initState() {
    super.initState();
    _loadData();
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  void _loadData() {
    setState(() {
      _categories = HomeDataService.getCategories();
      _hotels = HomeDataService.getMostPopularHotels();
      _filteredHotels = HomeDataService.getHotelsByCategory(_selectedCategory);
    });
  }

  void _onCategorySelected(CategoryModel category) {
    setState(() {
      // Update categories selection
      _categories = _categories.map((cat) {
        return cat.copyWith(isSelected: cat.id == category.id);
      }).toList();

      _selectedCategory = category.name;
      _filteredHotels = HomeDataService.getHotelsByCategory(_selectedCategory);
    });
  }

  void _onSearchChanged(String query) {
    setState(() {
      if (query.isEmpty) {
        _filteredHotels = HomeDataService.getHotelsByCategory(_selectedCategory);
      } else {
        _filteredHotels = HomeDataService.searchHotels(query);
      }
    });
  }

  void _onHotelTap(HotelModel hotel) {
    // TODO: Navigate to hotel details screen
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Tapped on ${hotel.name}'),
        backgroundColor: AppColors.primary,
      ),
    );
  }

  void _onFavoriteToggle(HotelModel hotel) {
    setState(() {
      final index = _filteredHotels.indexWhere((h) => h.id == hotel.id);
      if (index != -1) {
        _filteredHotels[index] = hotel.copyWith(isFavorite: !hotel.isFavorite);
      }
    });

    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          hotel.isFavorite
            ? 'Removed from favorites'
            : 'Added to favorites'
        ),
        backgroundColor: AppColors.primary,
        duration: const Duration(seconds: 1),
      ),
    );
  }

  void _onNavTap(int index) {
    setState(() {
      _currentNavIndex = index;
    });

    // TODO: Handle navigation to different screens
    final screens = ['Home', 'Explore', 'Favorites', 'Profile'];
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Navigate to ${screens[index]}'),
        backgroundColor: AppColors.primary,
        duration: const Duration(seconds: 1),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      body: SafeArea(
        child: Column(
          children: [
            // Main Content
            Expanded(
              child: CustomScrollView(
                slivers: [
                  // Header
                  _buildHeader(),

                  // Search Bar
                  _buildSearchSection(),

                  // Categories
                  _buildCategoriesSection(),

                  // Most Popular Section
                  _buildMostPopularSection(),
                ],
              ),
            ),

            // Bottom Navigation
            BottomNavigationWidget(
              currentIndex: _currentNavIndex,
              onTap: _onNavTap,
            ),
          ],
        ),
      ),
    );
  }
  SliverToBoxAdapter _buildHeader() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Hi, Welcome Back! 👋',
                        style: AppTextStyles.headlineSmall.copyWith(
                          color: AppColors.textPrimary,
                          fontWeight: FontWeight.w700,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Find your perfect stay',
                        style: AppTextStyles.bodyMedium.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ),
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: AppColors.surface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: AppColors.borderLight),
                  ),
                  child: Icon(
                    Icons.notifications_outlined,
                    color: AppColors.textSecondary,
                    size: 24,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  SliverToBoxAdapter _buildSearchSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.symmetric(horizontal: 20),
        child: SearchBarWidget(
          controller: _searchController,
          hintText: 'Search hotels, locations...',
          onChanged: _onSearchChanged,
        ),
      ),
    );
  }

  SliverToBoxAdapter _buildCategoriesSection() {
    return SliverToBoxAdapter(
      child: Container(
        height: 60,
        margin: const EdgeInsets.only(top: 24),
        child: ListView.builder(
          scrollDirection: Axis.horizontal,
          padding: const EdgeInsets.symmetric(horizontal: 20),
          itemCount: _categories.length,
          itemBuilder: (context, index) {
            return CategoryChipWidget(
              category: _categories[index],
              onTap: _onCategorySelected,
            );
          },
        ),
      ),
    );
  }

  SliverToBoxAdapter _buildMostPopularSection() {
    return SliverToBoxAdapter(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Most Popular',
                  style: AppTextStyles.headlineSmall.copyWith(
                    color: AppColors.textPrimary,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                TextButton(
                  onPressed: () {
                    // TODO: Navigate to see all hotels
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(
                        content: Text('See all hotels'),
                        backgroundColor: AppColors.primary,
                      ),
                    );
                  },
                  child: Text(
                    'See All',
                    style: AppTextStyles.bodyMedium.copyWith(
                      color: AppColors.primary,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),

            // Hotels List
            if (_filteredHotels.isEmpty)
              _buildEmptyState()
            else
              ListView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: _filteredHotels.length,
                itemBuilder: (context, index) {
                  return HotelCardWidget(
                    hotel: _filteredHotels[index],
                    onTap: _onHotelTap,
                    onFavoriteToggle: _onFavoriteToggle,
                  );
                },
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState() {
    return Container(
      padding: const EdgeInsets.all(40),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppColors.borderLight),
      ),
      child: Column(
        children: [
          Icon(
            Icons.hotel_outlined,
            size: 64,
            color: AppColors.textSecondary,
          ),
          const SizedBox(height: 16),
          Text(
            'No hotels found',
            style: AppTextStyles.titleMedium.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Try adjusting your search or category filter',
            style: AppTextStyles.bodyMedium.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

}
