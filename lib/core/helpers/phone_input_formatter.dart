import 'package:flutter/services.dart';

/// Custom input formatter for Tanzania phone numbers
/// Automatically formats input to +255 7XX XXX XXX or +255 6XX XXX XXX
class TanzaniaPhoneInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Extract only digits from the input
    String newDigits = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
    String oldDigits = oldValue.text.replaceAll(RegExp(r'[^\d]'), '');

    // If user is deleting, allow it
    if (newDigits.length < oldDigits.length) {
      return _formatPhoneNumber(newDigits);
    }

    // Limit to maximum 9 digits (after +255)
    if (newDigits.length > 9) {
      newDigits = newDigits.substring(0, 9);
    }

    // Validate first digit: must be 6 or 7
    if (newDigits.isNotEmpty && newDigits[0] != '6' && newDigits[0] != '7') {
      return oldValue; // Reject invalid first digit
    }

    return _formatPhoneNumber(newDigits);
  }
  
  TextEditingValue _formatPhoneNumber(String digits) {
    String formatted = '+255 ';

    if (digits.isNotEmpty) {
      // Add the first digit (should already be validated as 6 or 7)
      formatted += digits[0];

      // Add remaining digits with formatting
      if (digits.length > 1) {
        String remaining = digits.substring(1);

        // Add first group (2 digits)
        if (remaining.isNotEmpty) {
          formatted += remaining.substring(0, remaining.length >= 2 ? 2 : 1);
          if (remaining.length >= 2) {
            formatted += ' ';
          }
        }

        // Add second group (3 digits)
        if (remaining.length >= 3) {
          formatted += remaining.substring(2, remaining.length >= 5 ? 5 : remaining.length);
          if (remaining.length >= 5) {
            formatted += ' ';
          }
        }

        // Add third group (3 digits)
        if (remaining.length >= 6) {
          formatted += remaining.substring(5);
        }
      }
    }

    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}

/// Input formatter that only allows digits and limits length
class TanzaniaPhoneDigitsFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Only allow digits
    String newText = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
    
    // Limit to 9 digits (after +255)
    if (newText.length > 9) {
      newText = newText.substring(0, 9);
    }
    
    // Ensure first digit is 6 or 7
    if (newText.isNotEmpty && newText[0] != '6' && newText[0] != '7') {
      if (oldValue.text.isNotEmpty) {
        // Keep old value if new first digit is invalid
        return oldValue;
      } else {
        // Default to 7 if starting fresh
        newText = '7' + (newText.length > 1 ? newText.substring(1) : '');
      }
    }
    
    return TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: newText.length),
    );
  }
}
