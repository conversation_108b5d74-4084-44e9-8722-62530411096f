#!/bin/bash

# Create folders
mkdir -p app/{routes,theme} \
  core/{constants,di,extensions,helpers,services,utils,widgets} \
  features/{authentication/{data/{models,repositories,services},presentation/{providers,screens,widgets},routes},\
house_management/{data/{models,repositories,services},presentation/{providers,screens,widgets},routes},\
booking/{data/{models,repositories,services},presentation/{providers,screens,widgets},routes},\
payment/{data/{models,repositories,services},presentation/{providers,screens,widgets},routes},\
reviews/{data/{models,repositories,services},presentation/{providers,screens,widgets},routes},\
chat/{data/{models,repositories,services},presentation/{providers,screens,widgets},routes},\
onboarding/{data/models,presentation/{providers,screens,widgets},routes},\
shared/{data/{models,services},presentation/{providers,screens,widgets},routes}}

# Create files
touch app/app.dart \
  app/routes/app_routes.dart \
  app/theme/{app_theme.dart,app_colors.dart,app_text_styles.dart} \
  bootstrap.dart \
  core/constants/{api_constants.dart,app_constants.dart} \
  core/di/service_locator.dart \
  core/extensions/{context_extensions.dart,string_extensions.dart,date_extensions.dart} \
  core/helpers/{date_helpers.dart,validators.dart,formatters.dart,image_picker_helper.dart} \
  core/services/{analytics_service.dart,connectivity_service.dart,crash_service.dart,http_client.dart,navigation_service.dart,notification_service.dart,session_service.dart,storage_service.dart,theme_service.dart,chat_service.dart} \
  core/utils/logger.dart \
  core/widgets/{custom_app_bar.dart,custom_button.dart,custom_text_field.dart,error_widget.dart,loading_widget.dart,image_carousel.dart,rating_widget.dart} \
  features/authentication/data/models/{auth_response.dart,user_model.dart,login_request_model.dart,register_request_model.dart} \
  features/authentication/data/repositories/auth_repository.dart \
  features/authentication/data/services/auth_api_service.dart \
  features/authentication/presentation/providers/auth_provider.dart \
  features/authentication/presentation/screens/{login_screen.dart,register_screen.dart,forgot_password_screen.dart,profile_screen.dart} \
  features/authentication/presentation/widgets/{auth_button.dart,auth_text_field.dart,login_form.dart,register_form.dart,social_login_buttons.dart} \
  features/authentication/routes/auth_routes.dart \
  features/house_management/data/models/{house_model.dart,amenity_model.dart,house_image_model.dart} \
  features/house_management/data/repositories/house_repository.dart \
  features/house_management/data/services/house_api_service.dart \
  features/house_management/presentation/providers/house_provider.dart \
  features/house_management/presentation/screens/{houses_list_screen.dart,house_details_screen.dart,add_house_screen.dart,edit_house_screen.dart} \
  features/house_management/presentation/widgets/{house_card.dart,house_form.dart,image_upload_widget.dart} \
  features/house_management/routes/house_routes.dart \
  features/booking/data/models/{booking_model.dart,transport_model.dart} \
  features/booking/data/repositories/booking_repository.dart \
  features/booking/data/services/booking_api_service.dart \
  features/booking/presentation/providers/booking_provider.dart \
  features/booking/presentation/screens/{booking_form_screen.dart,bookings_list_screen.dart,booking_confirmation_screen.dart} \
  features/booking/presentation/widgets/{booking_form.dart,booking_card.dart,transport_selector.dart} \
  features/booking/routes/booking_routes.dart \
  features/payment/data/models/{payment_model.dart,payment_method_model.dart} \
  features/payment/data/repositories/payment_repository.dart \
  features/payment/data/services/payment_api_service.dart \
  features/payment/presentation/providers/payment_provider.dart \
  features/payment/presentation/screens/{payment_screen.dart,payment_success_screen.dart} \
  features/payment/presentation/widgets/{payment_form.dart,payment_method_selector.dart} \
  features/payment/routes/payment_routes.dart \
  features/reviews/data/models/review_model.dart \
  features/reviews/data/repositories/review_repository.dart \
  features/reviews/data/services/review_api_service.dart \
  features/reviews/presentation/providers/review_provider.dart \
  features/reviews/presentation/screens/{reviews_screen.dart,add_review_screen.dart} \
  features/reviews/presentation/widgets/{review_card.dart,review_form.dart} \
  features/reviews/routes/review_routes.dart \
  features/chat/data/models/{chat_model.dart,message_model.dart} \
  features/chat/data/repositories/chat_repository.dart \
  features/chat/data/services/chat_api_service.dart \
  features/chat/presentation/providers/chat_provider.dart \
  features/chat/presentation/screens/{chats_list_screen.dart,chat_screen.dart} \
  features/chat/presentation/widgets/{chat_bubble.dart,chat_list_tile.dart,message_input.dart} \
  features/chat/routes/chat_routes.dart \
  features/onboarding/data/models/onboarding_model.dart \
  features/onboarding/presentation/providers/onboarding_provider.dart \
  features/onboarding/presentation/screens/{splash_screen.dart,onboarding_screen.dart} \
  features/onboarding/presentation/widgets/{onboarding_page.dart,page_indicator.dart} \
  features/onboarding/routes/onboarding_routes.dart \
  features/shared/data/models/{api_response.dart,pagination_model.dart,location_model.dart,notification_model.dart} \
  features/shared/data/services/{location_service.dart,notification_service.dart} \
  features/shared/presentation/providers/{location_provider.dart,notification_provider.dart} \
  features/shared/presentation/screens/settings_screen.dart \
  features/shared/presentation/widgets/{bottom_nav_bar.dart,search_bar.dart,filter_widget.dart} \
  features/shared/routes/shared_routes.dart \
  main.dart

