class ApiConstants {
  // Base URLs
  static const String baseUrl = 'https://emakazi-backend-api.onrender.com';
  static const String baseUrlV1 = '$baseUrl/api';
  
  // Auth Endpoints---testing this one's
  static const String authLogin = '/auth/login';
  static const String authRegister = '/auth/register';
  static const String authRefresh = '/auth/refresh';
  static const String authForgotPassword = '/auth/forgot-password';
  static const String authResetPassword = '/auth/reset-password';
  static const String authVerifyEmail = '/auth/verify-email';
  static const String authVerifyPhone = '/auth/verify-phone';
  static const String authResendOtp = '/auth/resend-otp';
  static const String authLogout = '/auth/logout';
  
  
  
  static const String authProfile = '/auth/profile';

  // User Endpoints
  static const String users = '/auth/users';
  //static const String userProfile = '/auth/profile';
  static const String userUpdate = '/auth/update';
  static const String userVerification = '/users/verification';

  // Houses Endpoints
  static const String houses = '/houses';
  static const String housesSearch = '/houses/search';
  static const String housesFilter = '/houses/filter';
  static const String housesFeatured = '/houses/featured';
  static const String housesNearby = '/houses/nearby';
  static const String housesOwner = '/houses/owner';

  // Booking Endpoints
  static const String bookings = '/bookings';
  static const String bookingsUser = '/bookings/user';
  static const String bookingsOwner = '/bookings/owner';
  static const String bookingsCancel = '/bookings/cancel';
  static const String bookingsConfirm = '/bookings/confirm';

  // Payment Endpoints
  static const String payments = '/payments';
  static const String paymentsInitiate = '/payments/initiate';
  static const String paymentsVerify = '/payments/verify';
  static const String paymentsMethods = '/payments/methods';
  static const String paymentsHistory = '/payments/history';

  // Reviews Endpoints
  static const String reviews = '/reviews';
  static const String reviewsHouse = '/reviews/house';
  static const String reviewsUser = '/reviews/user';

  // Chat Endpoints
  static const String chat = '/chat';
  static const String chatConversations = '/chat/conversations';
  static const String chatMessages = '/chat/messages';
  static const String chatSend = '/chat/send';

  // Transport Endpoints
  static const String transport = '/transport';
  static const String transportProviders = '/transport/providers';
  static const String transportBooking = '/transport/booking';

  // Location Endpoints
  static const String locations = '/locations';
  static const String locationsRegions = '/locations/regions';
  static const String locationsDistricts = '/locations/districts';
  static const String locationsWards = '/locations/wards';

  // File Upload
  static const String upload = '/upload';
  static const String uploadImage = '/upload/image';
  static const String uploadDocument = '/upload/document';

  // Notifications
  static const String notifications = '/notifications';
  static const String notificationsMarkRead = '/notifications/mark-read';
  static const String notificationsSettings = '/notifications/settings';

  // Admin Endpoints
  static const String admin = '/admin';
  static const String adminUsers = '/admin/users';
  static const String adminHouses = '/admin/houses';
  static const String adminApprovals = '/admin/approvals';
  static const String adminReports = '/admin/reports';

  // Headers
  static const String headerContentType = 'Content-Type';
  static const String headerAuthorization = 'Authorization';
  static const String headerAccept = 'Accept';
  static const String headerUserAgent = 'User-Agent';

  // Content Types
  static const String contentTypeJson = 'application/json';
  static const String contentTypeFormData = 'multipart/form-data';

  // Error Codes
  static const int statusUnauthorized = 401;
  static const int statusForbidden = 403;
  static const int statusNotFound = 404;
  static const int statusValidationError = 422;
  static const int statusServerError = 500;
}