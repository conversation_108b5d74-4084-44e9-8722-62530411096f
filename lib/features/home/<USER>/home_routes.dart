import 'package:go_router/go_router.dart';
import '../presentation/screens/customer_home_screen.dart';
import '../presentation/screens/landlord_home_screen.dart';

class HomeRoutes {
  // Route paths
  static const String customerHome = '/customer-home';
  static const String landlordHome = '/landlord-home';

  // Route definitions
  static List<RouteBase> routes = [
    GoRoute(
      path: customerHome,
      name: 'customer-home',
      builder: (context, state) => const CustomerHomeScreen(),
    ),
    GoRoute(
      path: landlordHome,
      name: 'landlord-home',
      builder: (context, state) => const LandlordHomeScreen(),
    ),
  ];
}
