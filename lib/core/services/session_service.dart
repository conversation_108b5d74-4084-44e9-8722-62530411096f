import 'dart:async';
import 'package:flutter/foundation.dart';
import '../utils/logger.dart';
import 'storage_service.dart';
import 'navigation_service.dart';

enum UserRole { customer, household, admin }

class User {
  final String id;
  final String email;
  final String name;
  final UserRole role;
  final String? phoneNumber;
  final String? profileImageUrl;
  final bool isEmailVerified;
  final bool isPhoneVerified;
  final DateTime createdAt;
  final DateTime? lastLoginAt;

  const User({
    required this.id,
    required this.email,
    required this.name,
    required this.role,
    this.phoneNumber,
    this.profileImageUrl,
    required this.isEmailVerified,
    required this.isPhoneVerified,
    required this.createdAt,
    this.lastLoginAt,
  });

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json['id'] as String,
      email: json['email'] as String,
      name: json['name'] as String,
      role: UserRole.values.firstWhere(
        (e) => e.name == json['role'],
        orElse: () => UserRole.customer,
      ),
      phoneNumber: json['phone_number'] as String?,
      profileImageUrl: json['profile_image_url'] as String?,
      isEmailVerified: json['is_email_verified'] as bool? ?? false,
      isPhoneVerified: json['is_phone_verified'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      lastLoginAt: json['last_login_at'] != null
          ? DateTime.parse(json['last_login_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'role': role.name,
      'phone_number': phoneNumber,
      'profile_image_url': profileImageUrl,
      'is_email_verified': isEmailVerified,
      'is_phone_verified': isPhoneVerified,
      'created_at': createdAt.toIso8601String(),
      'last_login_at': lastLoginAt?.toIso8601String(),
    };
  }

  User copyWith({
    String? id,
    String? email,
    String? name,
    UserRole? role,
    String? phoneNumber,
    String? profileImageUrl,
    bool? isEmailVerified,
    bool? isPhoneVerified,
    DateTime? createdAt,
    DateTime? lastLoginAt,
  }) {
    return User(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      role: role ?? this.role,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      isEmailVerified: isEmailVerified ?? this.isEmailVerified,
      isPhoneVerified: isPhoneVerified ?? this.isPhoneVerified,
      createdAt: createdAt ?? this.createdAt,
      lastLoginAt: lastLoginAt ?? this.lastLoginAt,
    );
  }

  bool get isCustomer => role == UserRole.customer;
  bool get isHousehold => role == UserRole.household;
  bool get isAdmin => role == UserRole.admin;
  bool get isVerified => isEmailVerified && isPhoneVerified;
}

class SessionService extends ChangeNotifier {
  static SessionService? _instance;
  final StorageService _storage = StorageService.instance;
  
  User? _currentUser;
  String? _authToken;
  String? _refreshToken;
  Timer? _tokenRefreshTimer;
  
  // Stream controllers for reactive updates
  final StreamController<User?> _userController = StreamController<User?>.broadcast();
  final StreamController<bool> _authStateController = StreamController<bool>.broadcast();
  
  SessionService._();
  
  static SessionService get instance {
    _instance ??= SessionService._();
    return _instance!;
  }
  
  // Getters
  User? get currentUser => _currentUser;
  String? get authToken => _authToken;
  String? get refreshToken => _refreshToken;
  bool get isLoggedIn => _currentUser != null && _authToken != null;
  bool get isCustomer => _currentUser?.isCustomer ?? false;
  bool get isHousehold => _currentUser?.isHousehold ?? false;
  bool get isAdmin => _currentUser?.isAdmin ?? false;
  
  // Streams
  Stream<User?> get userStream => _userController.stream;
  Stream<bool> get authStateStream => _authStateController.stream;
  
  // Initialize session from stored data
  Future<void> initialize() async {
    try {
      Logger.logAuth('Initializing session');
      
      _authToken = _storage.getAuthToken();
      _refreshToken = _storage.getRefreshToken();
      
      final userData = _storage.getUserData();
      if (userData != null) {
        _currentUser = User.fromJson(userData);
        Logger.logAuth('Session restored', _currentUser!.id);
        
        _userController.add(_currentUser);
        _authStateController.add(true);
        
        // Start token refresh timer
        _startTokenRefreshTimer();
      } else {
        Logger.logAuth('No stored session found');
        _authStateController.add(false);
      }
    } catch (e) {
      Logger.logError('Session initialization failed', e);
      await clearSession();
    }
  }
  
  // Login user
  Future<void> login({
    required User user,
    required String authToken,
    required String refreshToken,
  }) async {
    try {
      Logger.logAuth('User logging in', user.id);
      
      _currentUser = user;
      _authToken = authToken;
      _refreshToken = refreshToken;
      
      // Store in persistent storage
      await _storage.setUserData(user.toJson());
      await _storage.setAuthToken(authToken);
      await _storage.setRefreshToken(refreshToken);
      await _storage.setUserRole(user.role.name);
      
      // Notify listeners
      notifyListeners();
      _userController.add(_currentUser);
      _authStateController.add(true);
      
      // Notify listeners
      _userController.add(_currentUser);
      _authStateController.add(true);
      
      // Start token refresh timer
      _startTokenRefreshTimer();
      
      Logger.logAuth('User logged in successfully', user.id);
    } catch (e) {
      Logger.logError('Login failed', e);
      rethrow;
    }
  }
  
  // Simple login method for testing (before implementing real auth API)
  Future<void> loginWithCredentials(String email, String password) async {
    try {
      // Create mock user for testing
      final user = User(
        id: 'test_${DateTime.now().millisecondsSinceEpoch}',
        email: email,
        name: email.split('@')[0],
        role: UserRole.customer,
        isEmailVerified: true,
        isPhoneVerified: false,
        createdAt: DateTime.now(),
        lastLoginAt: DateTime.now(),
      );
      
      // Mock tokens
      const authToken = 'mock_auth_token_123';
      const refreshToken = 'mock_refresh_token_456';
      
      await login(
        user: user,
        authToken: authToken,
        refreshToken: refreshToken,
      );
    } catch (e) {
      Logger.logError('Login failed', e);
      rethrow;
    }
  }
  
  // Logout user
  Future<void> logout() async {
    try {
      Logger.logAuth('User logging out', _currentUser?.id);
      
      // Cancel timer
      _tokenRefreshTimer?.cancel();
      
      // Clear session data
      await clearSession();
      
      // Navigate to login
      NavigationService.instance.pushToLogin();
      
      Logger.logAuth('User logged out successfully');
    } catch (e) {
      Logger.logError('Logout failed', e);
    }
  }
  
  // Clear session data
  Future<void> clearSession() async {
    _currentUser = null;
    _authToken = null;
    _refreshToken = null;
    _tokenRefreshTimer?.cancel();
    
    await _storage.clearAuthData();
    
    // Notify listeners
    notifyListeners();
    _userController.add(null);
    _authStateController.add(false);
  }
  
  // Update user data
  Future<void> updateUser(User updatedUser) async {
    try {
      _currentUser = updatedUser;
      await _storage.setUserData(updatedUser.toJson());
      _userController.add(_currentUser);
      
      Logger.logAuth('User data updated', updatedUser.id);
    } catch (e) {
      Logger.logError('User update failed', e);
      rethrow;
    }
  }
  
  // Update auth tokens
  Future<void> updateTokens({
    required String authToken,
    required String refreshToken,
  }) async {
    try {
      _authToken = authToken;
      _refreshToken = refreshToken;
      
      await _storage.setAuthToken(authToken);
      await _storage.setRefreshToken(refreshToken);
      
      Logger.logAuth('Tokens updated');
    } catch (e) {
      Logger.logError('Token update failed', e);
      rethrow;
    }
  }
  
  // Check if user has specific permissions
  bool hasPermission(String permission) {
    if (_currentUser == null) return false;
    
    switch (permission) {
      case 'create_house':
        return _currentUser!.isHousehold || _currentUser!.isAdmin;
      case 'manage_houses':
        return _currentUser!.isHousehold || _currentUser!.isAdmin;
      case 'book_house':
        return _currentUser!.isCustomer || _currentUser!.isAdmin;
      case 'admin_access':
        return _currentUser!.isAdmin;
      case 'view_analytics':
        return _currentUser!.isHousehold || _currentUser!.isAdmin;
      case 'manage_bookings':
        return _currentUser!.isHousehold || _currentUser!.isAdmin;
      default:
        return false;
    }
  }
  
  // Check if user profile is complete
  bool isProfileComplete() {
    if (_currentUser == null) return false;
    
    // Basic required fields
    if (_currentUser!.name.isEmpty || _currentUser!.email.isEmpty) {
      return false;
    }
    
    // Role-specific requirements
    if (_currentUser!.isHousehold) {
      return _currentUser!.isPhoneVerified && _currentUser!.isEmailVerified;
    }
    
    return _currentUser!.isEmailVerified;
  }
  
  // Get user's display name
  String getDisplayName() {
    if (_currentUser == null) return 'Guest';
    
    if (_currentUser!.name.isNotEmpty) {
      return _currentUser!.name;
    }
    
    return _currentUser!.email.split('@').first;
  }
  
  // Get user's role display name
  String getRoleDisplayName() {
    if (_currentUser == null) return 'Guest';
    
    switch (_currentUser!.role) {
      case UserRole.customer:
        return 'Customer';
      case UserRole.household:
        return 'Property Owner';
      case UserRole.admin:
        return 'Administrator';
    }
  }
  
  // Start automatic token refresh
  void _startTokenRefreshTimer() {
    _tokenRefreshTimer?.cancel();
    
    // Refresh token every 50 minutes (assuming 1-hour token expiry)
    _tokenRefreshTimer = Timer.periodic(
      const Duration(minutes: 50),
      (_) => _refreshAuthToken(),
    );
  }
  
  // Refresh authentication token
  Future<void> _refreshAuthToken() async {
    try {
      if (_refreshToken == null) {
        Logger.logAuth('No refresh token available');
        await logout();
        return;
      }
      
      Logger.logAuth('Refreshing auth token');
      
      // TODO: Implement actual token refresh API call
      // final response = await HttpClient.instance.post(
      //   ApiConstants.authRefresh,
      //   data: {'refresh_token': _refreshToken},
      // );
      // 
      // final newAuthToken = response['access_token'];
      // final newRefreshToken = response['refresh_token'];
      // 
      // await updateTokens(
      //   authToken: newAuthToken,
      //   refreshToken: newRefreshToken,
      // );
      
      Logger.logAuth('Auth token refreshed successfully');
    } catch (e) {
      Logger.logError('Token refresh failed', e);
      await logout();
    }
  }
  
  // Dispose resources
  @override
  void dispose() {
    _tokenRefreshTimer?.cancel();
    _userController.close();
    _authStateController.close();
    super.dispose();
  }
}