import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../../../../core/helpers/validators.dart';
import '../providers/auth_provider.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_text_field.dart';

class ProfileScreen extends StatefulWidget {
  const ProfileScreen({super.key});

  @override
  State<ProfileScreen> createState() => _ProfileScreenState();
}

class _ProfileScreenState extends State<ProfileScreen> {
  final _formKey = GlobalKey<FormState>();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  final _phoneController = TextEditingController();
  
  bool _isEditing = false;

  @override
  void initState() {
    super.initState();
    _loadUserData();
  }

  void _loadUserData() {
    final authProvider = context.read<AuthProvider>();
    final user = authProvider.currentUser;
    
    if (user != null) {
      _firstNameController.text = user.firstName;
      _lastNameController.text = user.lastName;
      _emailController.text = user.email;
      _phoneController.text = user.phoneNumber;
    }
  }

  @override
  void dispose() {
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    _phoneController.dispose();
    super.dispose();
  }

  void _updateProfile() async {
    if (!_formKey.currentState!.validate()) return;

    final userData = {
      'first_name': _firstNameController.text.trim(),
      'last_name': _lastNameController.text.trim(),
      'email': _emailController.text.trim(),
      // Note: Phone number update might require verification
    };

    final authProvider = context.read<AuthProvider>();
    final success = await authProvider.updateProfile(userData);

    if (success) {
      setState(() {
        _isEditing = false;
      });
      _showSnackBar(authProvider.successMessage ?? 'Profile updated successfully!', isError: false);
    } else if (authProvider.errorMessage != null) {
      _showSnackBar(authProvider.errorMessage!, isError: true);
    }
  }

  void _showSnackBar(String message, {required bool isError}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.error : AppColors.success,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        elevation: 0,
        title: Text(
          'Profile',
          style: AppTextStyles.headlineSmall.copyWith(
            color: AppColors.textOnPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        actions: [
          if (!_isEditing)
            IconButton(
              icon: const Icon(
                Icons.edit,
                color: AppColors.textOnPrimary,
              ),
              onPressed: () {
                setState(() {
                  _isEditing = true;
                });
              },
            ),
        ],
      ),
      body: Consumer<AuthProvider>(
        builder: (context, authProvider, child) {
          final user = authProvider.currentUser;
          
          if (user == null) {
            return const Center(
              child: Text('No user data available'),
            );
          }

          return SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Form(
              key: _formKey,
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Profile Header
                  _buildProfileHeader(user),
                  const SizedBox(height: 32),

                  // Profile Form
                  _buildProfileForm(),
                  const SizedBox(height: 32),

                  // Action Buttons
                  if (_isEditing) _buildActionButtons(),
                  
                  const SizedBox(height: 32),

                  // Logout Section
                  _buildLogoutSection(),
                ],
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildProfileHeader(user) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: const LinearGradient(
          colors: AppColors.primaryGradient,
        ),
        borderRadius: BorderRadius.circular(16),
      ),
      child: Column(
        children: [
          // Avatar
          Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: AppColors.textOnPrimary.withOpacity(0.2),
              shape: BoxShape.circle,
            ),
            child: Icon(
              user.role.value == 'landlord' ? Icons.home_work : Icons.person,
              size: 40,
              color: AppColors.textOnPrimary,
            ),
          ),
          const SizedBox(height: 16),

          // Name
          Text(
            user.fullName,
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.textOnPrimary,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 4),

          // Role
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
            decoration: BoxDecoration(
              color: AppColors.textOnPrimary.withOpacity(0.2),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Text(
              user.role.displayName,
              style: AppTextStyles.bodySmall.copyWith(
                color: AppColors.textOnPrimary,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildProfileForm() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Personal Information',
          style: AppTextStyles.headlineSmall.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 20),

        // First Name
        AuthTextField(
          label: 'First Name',
          hint: 'Enter your first name',
          controller: _firstNameController,
          validator: Validators.name,
          enabled: _isEditing,
          prefixIcon: const Icon(
            Icons.person_outline,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 20),

        // Last Name
        AuthTextField(
          label: 'Last Name',
          hint: 'Enter your last name',
          controller: _lastNameController,
          validator: Validators.name,
          enabled: _isEditing,
          prefixIcon: const Icon(
            Icons.person_outline,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 20),

        // Email
        AuthTextField(
          label: 'Email Address',
          hint: 'Enter your email address',
          controller: _emailController,
          validator: Validators.email,
          keyboardType: TextInputType.emailAddress,
          enabled: _isEditing,
          prefixIcon: const Icon(
            Icons.email_outlined,
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(height: 20),

        // Phone Number (Read-only for now)
        AuthTextField(
          label: 'Phone Number',
          hint: 'Your phone number',
          controller: _phoneController,
          enabled: false, // Phone number changes require verification
          prefixIcon: const Icon(
            Icons.phone,
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildActionButtons() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return Row(
          children: [
            Expanded(
              child: SecondaryButton(
                text: 'Cancel',
                onPressed: () {
                  setState(() {
                    _isEditing = false;
                  });
                  _loadUserData(); // Reset form data
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: AuthButton(
                text: 'Save Changes',
                onPressed: _updateProfile,
                isLoading: authProvider.isLoading,
                icon: Icons.save,
              ),
            ),
          ],
        );
      },
    );
  }

  Widget _buildLogoutSection() {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        color: AppColors.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: AppColors.border),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Account Actions',
            style: AppTextStyles.bodyLarge.copyWith(
              fontWeight: FontWeight.w600,
              color: AppColors.textPrimary,
            ),
          ),
          const SizedBox(height: 16),
          
          AuthButton(
            text: 'Logout',
            onPressed: () async {
              final confirmed = await _showLogoutDialog();
              if (confirmed == true) {
                final authProvider = context.read<AuthProvider>();
                await authProvider.logout();
                if (mounted) {
                  Navigator.pushNamedAndRemoveUntil(
                    context,
                    '/login',
                    (route) => false,
                  );
                }
              }
            },
            backgroundColor: AppColors.error,
            width: double.infinity,
            icon: Icons.logout,
          ),
        ],
      ),
    );
  }

  Future<bool?> _showLogoutDialog() {
    return showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        title: Text(
          'Confirm Logout',
          style: AppTextStyles.headlineSmall.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        content: Text(
          'Are you sure you want to logout?',
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'Cancel',
              style: AppTextStyles.bodyMedium.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ),
          AuthButton(
            text: 'Logout',
            onPressed: () => Navigator.pop(context, true),
            backgroundColor: AppColors.error,
          ),
        ],
      ),
    );
  }
}
