import 'user_model.dart';

class RegisterRequestModel {
  //fields inside the register modal
  final String phoneNumber;
  final String password;
  final String confirmPassword;
  final String firstName;
  final String lastName;
  final String email;
  final UserRole role;
  final OtpDeliveryMethod otpMethod;

  const RegisterRequestModel({
    required this.phoneNumber,
    required this.password,
    required this.confirmPassword,
    required this.firstName,
    required this.lastName,
    required this.email,
    required this.role,
    this.otpMethod = OtpDeliveryMethod.email,
  });

  Map<String, dynamic> toJson() {
    return {
      'phone_number': phoneNumber,
      'password': password,
      'confirm_password': confirmPassword,
      'first_name': firstName,
      'last_name': lastName,
      'email': email,
      'role': role.value,
      'otp_method': otpMethod.value,
    };
  }

  @override
  String toString() {
    return 'RegisterRequestModel(phoneNumber: $phoneNumber, firstName: $firstName, lastName: $lastName, email: $email, role: $role, otpMethod: $otpMethod)';
  }
}

enum OtpDeliveryMethod {
  //enums values but eacsh one creates the string
  sms('sms'),
  email('email');

  const OtpDeliveryMethod(this.value);
  final String value;

  static OtpDeliveryMethod fromString(String value) {
    switch (value.toLowerCase()) {
      case 'email':
        return OtpDeliveryMethod.email;
      case 'sms':
      default:
        return OtpDeliveryMethod.sms;
    }
  }

  String get displayName {
    switch (this) {
      case OtpDeliveryMethod.sms:
        return 'SMS';
      case OtpDeliveryMethod.email:
        return 'Email';
    }
  }

  @override
  String toString() => value;
}

class VerifyOtpRequestModel {
  final String email;
  final String otp;

  const VerifyOtpRequestModel({
    required this.email,
    required this.otp,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'otp': otp,
    };
  }

  @override
  String toString() {
    return 'VerifyOtpRequestModel(email : $email, otp: $otp)';
  }
}

class ForgotPasswordRequestModel {
  final String email;
  final OtpDeliveryMethod otpMethod;

  const ForgotPasswordRequestModel({
    required this.email,
    this.otpMethod = OtpDeliveryMethod.email,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'otp_method': otpMethod.value,
    };
  }

  @override
  String toString() {
    return 'ForgotPasswordRequestModel(email: $email, otpMethod: $otpMethod)';
  }
}

class ResetPasswordRequestModel {
  final String email;
  final String otp;
  final String newPassword;
  final String confirmPassword;

  const ResetPasswordRequestModel({
    required this.email,
    required this.otp,
    required this.newPassword,
    required this.confirmPassword,
  });

  Map<String, dynamic> toJson() {
    return {
      'email': email,
      'otp': otp,
      'new_password': newPassword,
      'confirm_password': confirmPassword,
    };
  }

  @override
  String toString() {
    return 'ResetPasswordRequestModel(email: $email, otp: $otp)';
  }
}
