import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'theme/app_theme.dart';
import 'routes/app_router.dart';
import '../core/services/session_service.dart';
import '../features/authentication/presentation/providers/auth_provider.dart';

class EmakaziApp extends StatelessWidget {
  const EmakaziApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiProvider(
      providers: [
        // Session provider
        ChangeNotifierProvider<SessionService>(
          create: (context) => SessionService.instance,
        ),
        // Auth provider
        ChangeNotifierProvider<AuthProvider>(
          create: (context) => AuthProvider(),
        ),
        // Add other providers as needed
      ],
      child: MaterialApp.router(
        title: 'EMakazi',
        debugShowCheckedModeBanner: false,

        // Theme configuration
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.light, // TODO: Make this configurable

        // Router configuration
        routerConfig: AppRouter.router,
        
        // System UI configuration
        builder: (context, child) {
          return AnnotatedRegion<SystemUiOverlayStyle>(
            value: SystemUiOverlayStyle(
              statusBarColor: Colors.transparent,
              statusBarIconBrightness: Brightness.light,
              systemNavigationBarColor: Theme.of(context).scaffoldBackgroundColor,
              systemNavigationBarIconBrightness: Brightness.dark,
            ),
            child: MediaQuery(
              // Prevent font scaling beyond reasonable limits
              data: MediaQuery.of(context).copyWith(
                textScaler: TextScaler.linear(MediaQuery.of(context).textScaler.scale(1.0).clamp(0.8, 1.2)),
              ),
              child: child ?? const SizedBox.shrink(),
            ),
          );
        },
      ),
    );
  }
}
