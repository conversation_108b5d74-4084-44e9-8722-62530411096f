import '../../../../core/utils/logger.dart';
import '../models/auth_response.dart';
import '../models/login_request_model.dart';
import '../models/register_request_model.dart';
import '../models/user_model.dart';

/// Dummy API service for testing authentication flow
/// This simulates real API calls with delays and mock responses
/// Replace this with real API calls when backend is ready
class DummyAuthApiService {
  // TODO: Replace these with real API endpoints when backend is ready
  static const String _baseUrl = 'https://api.emakazi.com/v1/auth';
  
  // Dummy users for testing
  static final List<UserModel> _dummyUsers = [
    UserModel(
      id: '1',
      firstName: 'John',
      lastName: 'Customer',
      email: '<EMAIL>',
      phoneNumber: '+255123456789',
      role: UserRole.customer,
      isVerified: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
    ),
    UserModel(
      id: '2',
      firstName: 'Jane',
      lastName: 'Landlord',
      email: '<EMAIL>',
      phoneNumber: '+255987654321',
      role: UserRole.landlord,
      isVerified: true,
      createdAt: DateTime.now().subtract(const Duration(days: 15)),
      updatedAt: DateTime.now(),
    ),
  ];

  // Simulate network delay
  Future<void> _simulateNetworkDelay() async {
    await Future.delayed(const Duration(milliseconds: 1500));
  }

  /// Register user - Dummy implementation
  /// TODO: Replace with real API call to $_baseUrl/register
  Future<AuthResponse> register(RegisterRequestModel request) async {
    try {
      Logger.info('DummyAuthApiService: Simulating user registration');
      await _simulateNetworkDelay();
      
      // Simulate validation
      if (request.email == '<EMAIL>') {
        throw Exception('Email already exists');
      }
      
      Logger.info('DummyAuthApiService: Registration successful');
      return const AuthResponse(
        success: true,
        message: 'Registration successful. Please verify your phone number.',
        data: null, // No auth data until verified
      );
    } catch (e) {
      Logger.error('DummyAuthApiService: Registration failed - $e');
      return AuthResponse(
        success: false,
        message: e.toString(),
        error: e.toString(),
      );
    }
  }

  /// Verify OTP - Dummy implementation
  /// TODO: Replace with real API call to $_baseUrl/verify-otp
  Future<AuthResponse> verifyOtp(VerifyOtpRequestModel request) async {
    try {
      Logger.info('DummyAuthApiService: Simulating OTP verification');
      await _simulateNetworkDelay();
      
      // Simulate OTP validation (accept any 6-digit code for testing)
      if (request.otp.length != 6) {
        throw Exception('Invalid OTP format');
      }
      
      if (request.otp == '000000') {
        throw Exception('Invalid OTP');
      }
      
      // Find or create user
      final user = _dummyUsers.firstWhere(
        (u) => u.email == request.email,
        orElse: () => UserModel(
          id: DateTime.now().millisecondsSinceEpoch.toString(),
          firstName: 'Test',
          lastName: 'User',
          email: request.email,
          phoneNumber: '+255123456789', // Default phone
          role: UserRole.customer,
          isVerified: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        ),
      );
      
      Logger.info('DummyAuthApiService: OTP verification successful');
      return AuthResponse(
        success: true,
        message: 'Phone number verified successfully',
        data: AuthData(
          accessToken: 'dummy_access_token_${user.id}',
          refreshToken: 'dummy_refresh_token_${user.id}',
          user: user.copyWith(isVerified: true),
          expiresIn: 3600,
          tokenType: 'Bearer',
        ),
      );
    } catch (e) {
      Logger.error('DummyAuthApiService: OTP verification failed - $e');
      return AuthResponse(
        success: false,
        message: e.toString(),
        error: e.toString(),
      );
    }
  }

  /// Resend OTP - Dummy implementation
  /// TODO: Replace with real API call to $_baseUrl/resend-otp
  Future<AuthResponse> resendOtp(String phoneNumber) async {
    try {
      Logger.info('DummyAuthApiService: Simulating OTP resend');
      await _simulateNetworkDelay();
      
      Logger.info('DummyAuthApiService: OTP resent successfully');
      return AuthResponse(
        success: true,
        message: 'OTP sent successfully to $phoneNumber',
      );
    } catch (e) {
      Logger.error('DummyAuthApiService: Resend OTP failed - $e');
      return AuthResponse(
        success: false,
        message: e.toString(),
        error: e.toString(),
      );
    }
  }

  /// Login user - Dummy implementation
  /// TODO: Replace with real API call to $_baseUrl/login
  Future<AuthResponse> login(LoginRequestModel request) async {
    try {
      Logger.info('DummyAuthApiService: Simulating user login');
      await _simulateNetworkDelay();
      
      // Find user by phone number
      final user = _dummyUsers.firstWhere(
        (u) => u.phoneNumber == request.phoneNumber,
        orElse: () => throw Exception('User not found'),
      );
      
      // Simulate password validation (accept any password for testing)
      if (request.password.length < 6) {
        throw Exception('Invalid password');
      }
      
      Logger.info('DummyAuthApiService: Login successful');
      return AuthResponse(
        success: true,
        message: 'Login successful',
        data: AuthData(
          accessToken: 'dummy_access_token_${user.id}',
          refreshToken: 'dummy_refresh_token_${user.id}',
          user: user,
          expiresIn: 3600,
          tokenType: 'Bearer',
        ),
      );
    } catch (e) {
      Logger.error('DummyAuthApiService: Login failed - $e');
      return AuthResponse(
        success: false,
        message: e.toString(),
        error: e.toString(),
      );
    }
  }

  /// Forgot password - Dummy implementation
  /// TODO: Replace with real API call to $_baseUrl/forgot-password
  Future<AuthResponse> forgotPassword(ForgotPasswordRequestModel request) async {
    try {
      Logger.info('DummyAuthApiService: Simulating forgot password');
      await _simulateNetworkDelay();
      
      // Check if user exists
      final userExists = _dummyUsers.any(
        (u) => u.email == request.email,
      );
      
      if (!userExists) {
        throw Exception('User not found');
      }
      
      Logger.info('DummyAuthApiService: Password reset OTP sent');
      return const AuthResponse(
        success: true,
        message: 'Password reset OTP sent successfully',
      );
    } catch (e) {
      Logger.error('DummyAuthApiService: Forgot password failed - $e');
      return AuthResponse(
        success: false,
        message: e.toString(),
        error: e.toString(),
      );
    }
  }

  /// Reset password - Dummy implementation
  /// TODO: Replace with real API call to $_baseUrl/reset-password
  Future<AuthResponse> resetPassword(ResetPasswordRequestModel request) async {
    try {
      Logger.info('DummyAuthApiService: Simulating password reset');
      await _simulateNetworkDelay();
      
      // Validate OTP (accept any 6-digit code for testing)
      if (request.otp.length != 6 || request.otp == '000000') {
        throw Exception('Invalid OTP');
      }
      
      if (request.newPassword.length < 6) {
        throw Exception('Password must be at least 6 characters');
      }
      
      Logger.info('DummyAuthApiService: Password reset successful');
      return const AuthResponse(
        success: true,
        message: 'Password reset successful',
      );
    } catch (e) {
      Logger.error('DummyAuthApiService: Password reset failed - $e');
      return AuthResponse(
        success: false,
        message: e.toString(),
        error: e.toString(),
      );
    }
  }

  /// Logout user - Dummy implementation
  /// TODO: Replace with real API call to $_baseUrl/logout
  Future<AuthResponse> logout() async {
    try {
      Logger.info('DummyAuthApiService: Simulating logout');
      await _simulateNetworkDelay();
      
      Logger.info('DummyAuthApiService: Logout successful');
      return const AuthResponse(
        success: true,
        message: 'Logout successful',
      );
    } catch (e) {
      Logger.error('DummyAuthApiService: Logout failed - $e');
      return AuthResponse(
        success: false,
        message: e.toString(),
        error: e.toString(),
      );
    }
  }
}
