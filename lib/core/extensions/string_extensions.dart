import 'package:flutter/material.dart';

extension StringExtensions on String {
  // Validation helpers
  bool get isEmail {
    return RegExp(r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$').hasMatch(this);
  }
  
  bool get isPhoneNumber {
    // Tanzania specific: +2557 or +2556 followed by 8 digits
    return RegExp(r'^\+255[67][0-9]{8}$').hasMatch(this);
  }
  
  bool get isStrongPassword {
    // At least 8 characters, 1 uppercase, 1 lowercase, 1 number, 1 special char
    return RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$').hasMatch(this);
  }
  
  bool get isNumeric {
    return RegExp(r'^[0-9]+$').hasMatch(this);
  }
  
  bool get isAlphabetic {
    return RegExp(r'^[a-zA-Z]+$').hasMatch(this);
  }
  
  bool get isAlphanumeric {
    return RegExp(r'^[a-zA-Z0-9]+$').hasMatch(this);
  }
  
  bool get isUrl {
    return RegExp(r'^https?:\/\/[^\s/$.?#].[^\s]*$').hasMatch(this);
  }
  
  // String manipulation
  String get capitalize {
    if (isEmpty) return this;
    return '${this[0].toUpperCase()}${substring(1).toLowerCase()}';
  }
  
  String get capitalizeWords {
    return split(' ').map((word) => word.capitalize).join(' ');
  }
  
  String get camelCase {
    if (isEmpty) return this;
    final words = split(RegExp(r'[_\s-]+'));
    if (words.isEmpty) return this;
    
    return words.first.toLowerCase() +
        words.skip(1).map((word) => word.capitalize).join('');
  }
  
  String get pascalCase {
    if (isEmpty) return this;
    return split(RegExp(r'[_\s-]+'))
        .map((word) => word.capitalize)
        .join('');
  }
  
  String get snakeCase {
    return replaceAllMapped(RegExp(r'[A-Z]'), (match) => '_${match.group(0)!.toLowerCase()}')
        .replaceAll(RegExp(r'[_\s-]+'), '_')
        .toLowerCase()
        .replaceFirst(RegExp(r'^_'), '');
  }
  
  String get kebabCase {
    return snakeCase.replaceAll('_', '-');
  }
  
  String get reversed {
    return split('').reversed.join('');
  }
  
  String removeSpaces() {
    return replaceAll(' ', '');
  }
  
  String removeNumbers() {
    return replaceAll(RegExp(r'[0-9]'), '');
  }
  
  String removeSpecialCharacters() {
    return replaceAll(RegExp(r'[^a-zA-Z0-9\s]'), '');
  }
  
  String removeEmojis() {
    return replaceAll(RegExp(
      r'[\u{1f600}-\u{1f64f}]|[\u{1f300}-\u{1f5ff}]|[\u{1f680}-\u{1f6ff}]|[\u{1f1e0}-\u{1f1ff}]|[\u{2600}-\u{26ff}]|[\u{2700}-\u{27bf}]',
      unicode: true,
    ), '');
  }
  
  // Text truncation
  String truncate(int maxLength, [String suffix = '...']) {
    if (length <= maxLength) return this;
    return '${substring(0, maxLength)}$suffix';
  }
  
  String truncateWords(int maxWords, [String suffix = '...']) {
    final words = split(' ');
    if (words.length <= maxWords) return this;
    return '${words.take(maxWords).join(' ')}$suffix';
  }
  
  // Encoding/Decoding
  String get base64Encoded {
    return RegExp(r'^[A-Za-z0-9+/]*={0,2}$').hasMatch(this) ? this : '';
  }
  
  // File helpers
  String get fileExtension {
    final index = lastIndexOf('.');
    return index != -1 ? substring(index + 1).toLowerCase() : '';
  }
  
  String get fileName {
    final index = lastIndexOf('/');
    return index != -1 ? substring(index + 1) : this;
  }
  
  String get fileNameWithoutExtension {
    final name = fileName;
    final index = name.lastIndexOf('.');
    return index != -1 ? name.substring(0, index) : name;
  }
  
  // Currency formatting
  String formatAsCurrency([String symbol = 'TSh', String locale = 'en_TZ']) {
    final number = double.tryParse(replaceAll(',', ''));
    if (number == null) return this;
    
    // Simple formatting for now - can be enhanced with intl package
    final formatted = number.toStringAsFixed(2);
    final parts = formatted.split('.');
    final integerPart = parts[0];
    final decimalPart = parts[1];
    
    // Add thousand separators
    final regex = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    final formattedInteger = integerPart.replaceAllMapped(regex, (match) => '${match[1]},');
    
    return '$symbol $formattedInteger.$decimalPart';
  }
  
  // Phone number formatting - Tanzania specific for +2557 and +2556
  String formatPhoneNumber() {
    // Remove all non-digits and plus sign
    final cleanNumber = replaceAll(RegExp(r'[^\d+]'), '');

    // If it's already in correct format, format with spaces
    if (cleanNumber.startsWith('+255') && cleanNumber.length == 13) {
      // +255 7XX XXX XXX or +255 6XX XXX XXX
      return '${cleanNumber.substring(0, 4)} ${cleanNumber.substring(4, 7)} ${cleanNumber.substring(7, 10)} ${cleanNumber.substring(10)}';
    }

    // If it starts with 255 (without +), add + and format
    if (cleanNumber.startsWith('255') && cleanNumber.length == 12) {
      return '+${cleanNumber.substring(0, 3)} ${cleanNumber.substring(3, 4)} ${cleanNumber.substring(4, 7)} ${cleanNumber.substring(7, 10)} ${cleanNumber.substring(10)}';
    }

    // If it starts with 07 or 06, convert to +2557 or +2556
    if (cleanNumber.startsWith('07') && cleanNumber.length == 10) {
      final withoutZero = cleanNumber.substring(1); // Remove leading 0
      return '+255 ${withoutZero.substring(0, 1)} ${withoutZero.substring(1, 4)} ${withoutZero.substring(4, 7)} ${withoutZero.substring(7)}';
    }
    if (cleanNumber.startsWith('06') && cleanNumber.length == 10) {
      final withoutZero = cleanNumber.substring(1); // Remove leading 0
      return '+255 ${withoutZero.substring(0, 1)} ${withoutZero.substring(1, 4)} ${withoutZero.substring(4, 7)} ${withoutZero.substring(7)}';
    }

    return this;
  }

  // Convert phone number to standard +255 format
  String toStandardPhoneFormat() {
    final cleanNumber = replaceAll(RegExp(r'[^\d+]'), '');

    // Already in correct format
    if (cleanNumber.startsWith('+255') && cleanNumber.length == 13) {
      return cleanNumber;
    }

    // Starts with 255 (without +)
    if (cleanNumber.startsWith('255') && cleanNumber.length == 12) {
      return '+$cleanNumber';
    }

    // Starts with 07 or 06 (local format)
    if ((cleanNumber.startsWith('07') || cleanNumber.startsWith('06')) && cleanNumber.length == 10) {
      return '+255${cleanNumber.substring(1)}'; // Remove leading 0 and add +255
    }

    return this;
  }
  
  // Color conversion
  Color? get toColor {
    // Support hex colors like #FF0000 or FF0000
    String hex = replaceFirst('#', '');
    if (hex.length == 6) {
      hex = 'FF$hex'; // Add alpha channel
    }
    if (hex.length == 8) {
      final value = int.tryParse(hex, radix: 16);
      return value != null ? Color(value) : null;
    }
    return null;
  }
  
  // Markdown-like formatting
  String get bold => '**$this**';
  String get italic => '*$this*';
  String get strikethrough => '~~$this~~';
  String get code => '`$this`';
  
  // Social media helpers
  String get extractHashtags {
    final regex = RegExp(r'#\w+');
    return regex.allMatches(this)
        .map((match) => match.group(0))
        .join(' ');
  }
  
  String get extractMentions {
    final regex = RegExp(r'@\w+');
    return regex.allMatches(this)
        .map((match) => match.group(0))
        .join(' ');
  }
  
  List<String> get extractUrls {
    final regex = RegExp(r'https?:\/\/[^\s/$.?#].[^\s]*');
    return regex.allMatches(this)
        .map((match) => match.group(0)!)
        .toList();
  }
  
  // Search helpers
  bool containsIgnoreCase(String other) {
    return toLowerCase().contains(other.toLowerCase());
  }
  
  bool startsWithIgnoreCase(String other) {
    return toLowerCase().startsWith(other.toLowerCase());
  }
  
  bool endsWithIgnoreCase(String other) {
    return toLowerCase().endsWith(other.toLowerCase());
  }
  
  // Word count
  int get wordCount {
    return trim().split(RegExp(r'\s+')).where((word) => word.isNotEmpty).length;
  }
  
  // Character frequency
  Map<String, int> get characterFrequency {
    final frequency = <String, int>{};
    for (final char in split('')) {
      frequency[char] = (frequency[char] ?? 0) + 1;
    }
    return frequency;
  }
  
  // Simple pluralization
  String pluralize(int count) {
    if (count == 1) return this;
    
    // Simple English pluralization rules
    if (endsWith('y') && !['a', 'e', 'i', 'o', 'u'].contains(this[length - 2])) {
      return '${substring(0, length - 1)}ies';
    } else if (endsWith('s') || endsWith('sh') || endsWith('ch') || endsWith('x') || endsWith('z')) {
      return '${this}es';
    } else if (endsWith('f')) {
      return '${substring(0, length - 1)}ves';
    } else if (endsWith('fe')) {
      return '${substring(0, length - 2)}ves';
    }
    
    return '${this}s';
  }
  
  // Safe operations
  String get safe => this;
  
  String? get nullIfEmpty => isEmpty ? null : this;
  
  String get emptyIfNull => this;
  
  // Distance calculation for search
  int levenshteinDistance(String other) {
    if (this == other) return 0;
    if (isEmpty) return other.length;
    if (other.isEmpty) return length;
    
    final matrix = List.generate(
      length + 1,
      (i) => List.generate(other.length + 1, (j) => 0),
    );
    
    for (int i = 0; i <= length; i++) {
      matrix[i][0] = i;
    }
    
    for (int j = 0; j <= other.length; j++) {
      matrix[0][j] = j;
    }
    
    for (int i = 1; i <= length; i++) {
      for (int j = 1; j <= other.length; j++) {
        final cost = this[i - 1] == other[j - 1] ? 0 : 1;
        matrix[i][j] = [
          matrix[i - 1][j] + 1,
          matrix[i][j - 1] + 1,
          matrix[i - 1][j - 1] + cost,
        ].reduce((a, b) => a < b ? a : b);
      }
    }
    
    return matrix[length][other.length];
  }
}