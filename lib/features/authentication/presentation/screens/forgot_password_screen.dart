import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../../../../core/helpers/validators.dart';
import '../../data/models/register_request_model.dart';
import '../providers/auth_provider.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_text_field.dart';
import '../widgets/role_dropdown.dart';

class ForgotPasswordScreen extends StatefulWidget {
  const ForgotPasswordScreen({super.key});

  @override
  State<ForgotPasswordScreen> createState() => _ForgotPasswordScreenState();
}

class _ForgotPasswordScreenState extends State<ForgotPasswordScreen> {
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  OtpDeliveryMethod _otpMethod = OtpDeliveryMethod.sms;

  @override
  void dispose() {
    _phoneController.dispose();
    super.dispose();
  }

  void _sendResetCode() async {
    if (!_formKey.currentState!.validate()) return;

    final request = ForgotPasswordRequestModel(
      email: _phoneController.text.trim(), // This should be email input
      otpMethod: _otpMethod,
    );

    final authProvider = context.read<AuthProvider>();
    final success = await authProvider.forgotPassword(request);

    if (success) {
      if (mounted) {
        Navigator.pushNamed(context, '/reset-password');
      }
    } else if (authProvider.errorMessage != null) {
      _showSnackBar(authProvider.errorMessage!, isError: true);
    }
  }

  void _showSnackBar(String message, {required bool isError}) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: isError ? AppColors.error : AppColors.success,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(Icons.arrow_back_ios),
          onPressed: () => Navigator.pop(context),
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                _buildHeader(),
                const SizedBox(height: 48),

                // Form Fields
                _buildFormFields(),
                const SizedBox(height: 32),

                // Send Code Button
                _buildSendCodeButton(),
                const SizedBox(height: 24),

                // Back to Login Link
                _buildBackToLoginLink(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Icon
        Container(
          width: 80,
          height: 80,
          decoration: BoxDecoration(
            color: AppColors.warning.withOpacity(0.1),
            shape: BoxShape.circle,
          ),
          child: const Icon(
            Icons.lock_reset,
            size: 40,
            color: AppColors.warning,
          ),
        ),
        const SizedBox(height: 24),

        // Title
        Text(
          'Forgot Password?',
          style: AppTextStyles.headlineLarge.copyWith(
            fontWeight: FontWeight.bold,
            color: AppColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),

        // Subtitle
        Text(
          'No worries! Enter your phone number and we\'ll send you a reset code.',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        // Phone Number
        PhoneTextField(
          label: 'Phone Number',
          hint: 'Enter your registered phone number',
          controller: _phoneController,
          validator: Validators.phoneNumber,
        ),
        const SizedBox(height: 20),

        // OTP Method Selection
        OtpMethodDropdown(
          label: 'Send reset code via',
          value: _otpMethod,
          onChanged: (method) {
            if (method != null) {
              setState(() {
                _otpMethod = method;
              });
            }
          },
        ),
      ],
    );
  }

  Widget _buildSendCodeButton() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return AuthButton(
          text: 'Send Reset Code',
          onPressed: _sendResetCode,
          isLoading: authProvider.isLoading,
          width: double.infinity,
          icon: Icons.send,
        );
      },
    );
  }

  Widget _buildBackToLoginLink() {
    return Center(
      child: RichText(
        text: TextSpan(
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
          children: [
            const TextSpan(text: 'Remember your password? '),
            WidgetSpan(
              child: GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Text(
                  'Back to Login',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                    decoration: TextDecoration.underline,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
