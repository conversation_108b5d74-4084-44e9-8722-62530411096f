import 'dart:async';
import 'package:flutter/foundation.dart';
import '../../../../core/utils/logger.dart';
import '../../data/models/login_request_model.dart';
import '../../data/models/register_request_model.dart';
import '../../data/models/user_model.dart';
import '../../data/repositories/auth_repository.dart';

enum AuthState {
  initial,
  loading,
  authenticated,
  unauthenticated,
  otpRequired,
  error,
}

class AuthProvider extends ChangeNotifier {
  //auth request bridge
  final AuthRepository _authRepository;
  
  AuthState _state = AuthState.initial;
  //user data
  UserModel? _currentUser;
  String? _errorMessage;
  String? _successMessage;
  String? _tempEmail; // For OTP flow-change to email 
  
  AuthProvider({AuthRepository? authRepository})
      : _authRepository = authRepository ?? AuthRepositoryImpl();

  // Getters
  AuthState get state => _state;
  UserModel? get currentUser => _currentUser;
  String? get errorMessage => _errorMessage;
  String? get tempPhoneNumber => _tempEmail; // For backward compatibility, returns email
  String? get successMessage => _successMessage;
  String? get tempEmail => _tempEmail;
  bool get isLoading => _state == AuthState.loading;
  bool get isAuthenticated => _state == AuthState.authenticated;
  bool get isOtpRequired => _state == AuthState.otpRequired;

  // Initialize auth state
  //happen in the app starts
  Future<void> initializeAuth() async {
    try {
      _setState(AuthState.loading);
      
      final isLoggedIn = await _authRepository.isLoggedIn();
      if (isLoggedIn) {
        _currentUser = await _authRepository.getCurrentUser();
        _setState(AuthState.authenticated);
        Logger.info('AuthProvider: User is already logged in');
      } else {
        _setState(AuthState.unauthenticated);
        Logger.info('AuthProvider: User is not logged in');
      }
    } catch (e) {
      Logger.error('AuthProvider: Initialize auth failed - $e');
      _setState(AuthState.unauthenticated);
    }
  }

  // Register user
  Future<bool> register(RegisterRequestModel request) async {
    try {
      print('=== AUTH PROVIDER: REGISTER START ===');
      print('Request: ${request.toString()}');
      print('Request JSON: ${request.toJson()}');
      Logger.info('=== REGISTRATION FLOW START ===');
      Logger.info('AuthProvider: Starting registration process');
      Logger.info('Request data: ${request.toString()}');
      Logger.info('Request JSON: ${request.toJson()}');

      _setState(AuthState.loading);
      _clearMessages();

      print('AuthProvider: Calling auth repository register method');
      Logger.info('AuthProvider: Calling auth repository register method');
      final response = await _authRepository.register(request);

      print('AuthProvider: Received response from repository');
      print('Response success: ${response.success}');
      print('Response message: ${response.message}');
      Logger.info('AuthProvider: Received response from repository');
      Logger.info('Response success: ${response.success}');
      Logger.info('Response message: ${response.message}');
      Logger.info('Response error: ${response.error}');
      Logger.info('Response data: ${response.data}');

      if (response.success) {
        // Store email for OTP flow
        _tempEmail = request.email;
        _successMessage = response.message ?? 'Registration successful! Please verify your OTP.';
        _setState(AuthState.otpRequired);
        Logger.info('AuthProvider: Registration successful, OTP required');
        Logger.info('Stored temp email: $_tempEmail');
        Logger.info('=== REGISTRATION FLOW SUCCESS ===');
        return true;
      } else {
        _errorMessage = response.error ?? response.message ?? 'Registration failed';
        _setState(AuthState.error);
        Logger.error('AuthProvider: Registration failed - ${_errorMessage}');
        Logger.error('=== REGISTRATION FLOW FAILED ===');
        return false;
      }
    } catch (e, stackTrace) {
      Logger.error('AuthProvider: Registration error - $e');
      Logger.error('Stack trace: $stackTrace');
      _errorMessage = 'Registration failed. Please try again.';
      _setState(AuthState.error);
      Logger.error('=== REGISTRATION FLOW ERROR ===');
      return false;
    }
  }

  // Verify OTP
  Future<bool> verifyOtp(String otp) async {
    try {
      if (_tempEmail == null) {
        _errorMessage = 'email number not found. Please register again.';
        _setState(AuthState.error);
        return false;
      }

      _setState(AuthState.loading);
      _clearMessages();
      
      //model ---hanadle the data and everthing in it very well 
      final request = VerifyOtpRequestModel(
        email: _tempEmail!,
        otp: otp,
      );
      
      final response = await _authRepository.verifyOtp(request);
      //otp verification should return the user from it 
      if (response.success && response.data?.user != null) {
        _currentUser = response.data!.user;
        _successMessage = response.message ?? 'Account verified successfully!';
        _setState(AuthState.authenticated);
        _tempEmail = null; // Clear temp phone number
        Logger.info('AuthProvider: OTP verification successful');
        return true;
      } else {
        _errorMessage = response.error ?? response.message ?? 'OTP verification failed';
        _setState(AuthState.error);
        Logger.error('AuthProvider: OTP verification failed - ${_errorMessage}');
        return false;
      }
    } catch (e) {
      Logger.error('AuthProvider: OTP verification error - $e');
      _errorMessage = 'OTP verification failed. Please try again.';
      _setState(AuthState.error);
      return false;
    }
  }

  // Resend OTP
  Future<bool> resendOtp() async {
    try {
      if (_tempEmail == null) {
        _errorMessage = 'Phone number not found. Please register again.';
        _setState(AuthState.error);
        return false;
      }

      _setState(AuthState.loading);
      _clearMessages();
      
      final response = await _authRepository.resendOtp(_tempEmail!);
      
      if (response.success) {
        _successMessage = response.message ?? 'OTP sent successfully!';
        _setState(AuthState.otpRequired);
        Logger.info('AuthProvider: OTP resent successfully');
        return true;
      } else {
        _errorMessage = response.error ?? response.message ?? 'Failed to resend OTP';
        _setState(AuthState.error);
        Logger.error('AuthProvider: Resend OTP failed - ${_errorMessage}');
        return false;
      }
    } catch (e) {
      Logger.error('AuthProvider: Resend OTP error - $e');
      _errorMessage = 'Failed to resend OTP. Please try again.';
      _setState(AuthState.error);
      return false;
    }
  }

  // Login user
  Future<bool> login(LoginRequestModel request) async {
    try {
      _setState(AuthState.loading);
      _clearMessages();
      
      final response = await _authRepository.login(request);
      
      if (response.success && response.data?.user != null) {
        _currentUser = response.data!.user;
        _successMessage = response.message ?? 'Login successful!';
        _setState(AuthState.authenticated);
        Logger.info('AuthProvider: Login successful');
        return true;
      } else {
        _errorMessage = response.error ?? response.message ?? 'Login failed';
        _setState(AuthState.error);
        Logger.error('AuthProvider: Login failed - ${_errorMessage}');
        return false;
      }
    } catch (e) {
      Logger.error('AuthProvider: Login error - $e');
      _errorMessage = 'Login failed. Please check your credentials.';
      _setState(AuthState.error);
      return false;
    }
  }

  // Forgot password
  Future<bool> forgotPassword(ForgotPasswordRequestModel request) async {
    try {
      _setState(AuthState.loading);
      _clearMessages();
      
      final response = await _authRepository.forgotPassword(request);
      
      if (response.success) {
        _tempEmail = request.email;
        _successMessage = response.message ?? 'Password reset OTP sent successfully!';
        _setState(AuthState.otpRequired);
        Logger.info('AuthProvider: Forgot password OTP sent');
        return true;
      } else {
        _errorMessage = response.error ?? response.message ?? 'Failed to send reset OTP';
        _setState(AuthState.error);
        Logger.error('AuthProvider: Forgot password failed - ${_errorMessage}');
        return false;
      }
    } catch (e) {
      Logger.error('AuthProvider: Forgot password error - $e');
      _errorMessage = 'Failed to send reset OTP. Please try again.';
      _setState(AuthState.error);
      return false;
    }
  }

  // Reset password
  Future<bool> resetPassword(String phone, String newPassword, String otp) async {
    _setState(AuthState.loading);
    _clearMessages();

    try {
      final request = ResetPasswordRequestModel(
        email: phone, // This should be email, but keeping parameter name for now
        otp: otp,
        newPassword: newPassword,
        confirmPassword: newPassword,
      );

      final response = await _authRepository.resetPassword(request);

      if (response.success) {
        _successMessage = response.message ?? 'Password reset successfully!';
        _setState(AuthState.unauthenticated);
        return true;
      } else {
        _errorMessage = response.message ?? 'Failed to reset password';
        _setState(AuthState.error);
        return false;
      }
    } catch (e) {
      _errorMessage = 'An error occurred. Please try again.';
      _setState(AuthState.error);
      return false;
    }
  }

  // Update profile
  Future<bool> updateProfile(Map<String, dynamic> userData) async {
    _setState(AuthState.loading);
    _clearMessages();

    try {
      final response = await _authRepository.updateUserProfile(userData);

      if (response.success) {
        // Update current user data if available
        if (response.data?.user != null) {
          _currentUser = response.data!.user;
        }
        _successMessage = response.message ?? 'Profile updated successfully!';
        _setState(AuthState.authenticated);
        return true;
      } else {
        _errorMessage = response.message ?? 'Failed to update profile';
        _setState(AuthState.error);
        return false;
      }
    } catch (e) {
      _errorMessage = 'An error occurred. Please try again.';
      _setState(AuthState.error);
      return false;
    }
  }

  // Logout user
  Future<void> logout() async {
    try {
      _setState(AuthState.loading);
      
      await _authRepository.logout();
      
      _currentUser = null;
      _tempEmail = null;
      _clearMessages();
      _setState(AuthState.unauthenticated);
      Logger.info('AuthProvider: Logout successful');
    } catch (e) {
      Logger.error('AuthProvider: Logout error - $e');
      // Still clear local state even if API call fails
      _currentUser = null;
      _tempEmail = null;
      _clearMessages();
      _setState(AuthState.unauthenticated);
    }
  }

  // Refresh user data
  Future<void> refreshUserData() async {
    try {
      if (_state != AuthState.authenticated) return;
      
      final response = await _authRepository.getUserProfile();
      if (response.success && response.data?.user != null) {
        _currentUser = response.data!.user;
        notifyListeners();
        Logger.info('AuthProvider: User data refreshed');
      }
    } catch (e) {
      Logger.error('AuthProvider: Refresh user data error - $e');
    }
  }

  // Clear error message
  void clearError() {
    _errorMessage = null;
    if (_state == AuthState.error) {
      _setState(AuthState.unauthenticated);
    }
  }

  // Clear success message
  void clearSuccess() {
    _successMessage = null;
    notifyListeners();
  }

  // Helper methods
  //it set the state and notify the listeners to rebuild ...
  void _setState(AuthState newState) {
    _state = newState;
    notifyListeners();
  }

  void _clearMessages() {
    _errorMessage = null;
    _successMessage = null;
  }

  // Get home route based on user role
  String getHomeRoute() {
    if (_currentUser == null) return '/login';
    
    switch (_currentUser!.role) {
      case UserRole.landlord:
        return '/landlord-home';
      case UserRole.customer:
        return '/customer-home';
    }
  }
}
