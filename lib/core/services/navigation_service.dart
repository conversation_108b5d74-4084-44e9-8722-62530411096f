import 'package:flutter/material.dart';
import '../utils/logger.dart';

class NavigationService {
  static NavigationService? _instance;
  static GlobalKey<NavigatorState>? _navigatorKey;

  NavigationService._();

  static NavigationService get instance {
    _instance ??= NavigationService._();
    return _instance!;
  }

  static GlobalKey<NavigatorState> get navigator<PERSON>ey {
    _navigatorKey ??= GlobalKey<NavigatorState>();
    return _navigatorKey!;
  }

  BuildContext? get currentContext => _navigatorKey?.currentContext;

  NavigatorState? get navigator => _navigatorKey?.currentState;

  // Basic navigation methods
  Future<T?> pushNamed<T extends Object?>(
    String routeName, {
    Object? arguments,
  }) {
    Logger.logNavigation('current', routeName);
    return navigator!.pushNamed<T>(routeName, arguments: arguments);
  }

  Future<T?> pushReplacementNamed<T extends Object?, TO extends Object?>(
    String routeName, {
    Object? arguments,
    TO? result,
  }) {
    Logger.logNavigation('current (replaced)', routeName);
    return navigator!.pushReplacementNamed<T, TO>(
      routeName,
      arguments: arguments,
      result: result,
    );
  }

  Future<T?> pushNamedAndRemoveUntil<T extends Object?>(
    String routeName,
    RoutePredicate predicate, {
    Object? arguments,
  }) {
    Logger.logNavigation('current (cleared stack)', routeName);
    return navigator!.pushNamedAndRemoveUntil<T>(
      routeName,
      predicate,
      arguments: arguments,
    );
  }

  void pop<T extends Object?>([T? result]) {
    Logger.logNavigation('current', 'back');
    navigator!.pop<T>(result);
  }

  void popUntil(RoutePredicate predicate) {
    Logger.logNavigation('current', 'back to condition');
    navigator!.popUntil(predicate);
  }

  bool canPop() {
    return navigator!.canPop();
  }

  void maybePop<T extends Object?>([T? result]) {
    if (canPop()) {
      pop<T>(result);
    }
  }

  // Convenience methods for common navigation patterns
  Future<T?> pushAndClearStack<T extends Object?>(String routeName, {Object? arguments}) {
    return pushNamedAndRemoveUntil<T>(
      routeName,
      (route) => false,
      arguments: arguments,
    );
  }

  Future<T?> pushToLogin<T extends Object?>() {
    return pushAndClearStack<T>('/login');
  }

  Future<T?> pushToHome<T extends Object?>() {
    return pushAndClearStack<T>('/home');
  }

  Future<T?> pushToOnboarding<T extends Object?>() {
    return pushAndClearStack<T>('/onboarding');
  }

  // Dialog methods
  Future<T?> showDialog<T>({
    required Widget dialog,
    bool barrierDismissible = true,
    Color? barrierColor,
    String? barrierLabel,
  }) {
    return showGeneralDialog<T>(
      context: currentContext!,
      barrierDismissible: barrierDismissible,
      barrierColor: barrierColor ?? Colors.black54,
      barrierLabel: barrierLabel,
      pageBuilder: (context, animation, secondaryAnimation) => dialog,
      transitionBuilder: (context, animation, secondaryAnimation, child) {
        return ScaleTransition(
          scale: CurvedAnimation(
            parent: animation,
            curve: Curves.easeInOut,
          ),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 200),
    );
  }

  Future<T?> showBottomSheet<T>({
    required Widget bottomSheet,
    bool isScrollControlled = false,
    bool isDismissible = true,
    bool enableDrag = true,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
  }) {
    return showModalBottomSheet<T>(
      context: currentContext!,
      builder: (context) => bottomSheet,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: shape,
    );
  }

  // Snackbar methods
  void showSnackBar({
    required String message,
    String? actionLabel,
    VoidCallback? onActionPressed,
    Duration duration = const Duration(seconds: 4),
    SnackBarBehavior behavior = SnackBarBehavior.floating,
  }) {
    final scaffoldMessenger = ScaffoldMessenger.of(currentContext!);
    scaffoldMessenger.hideCurrentSnackBar();
    
    scaffoldMessenger.showSnackBar(
      SnackBar(
        content: Text(message),
        duration: duration,
        behavior: behavior,
        action: actionLabel != null
            ? SnackBarAction(
                label: actionLabel,
                onPressed: onActionPressed ?? () {},
              )
            : null,
      ),
    );
  }

  void showSuccessSnackBar(String message) {
    showSnackBar(message: '✅ $message');
  }

  void showErrorSnackBar(String message) {
    showSnackBar(message: '❌ $message');
  }

  void showWarningSnackBar(String message) {
    showSnackBar(message: '⚠️ $message');
  }

  void showInfoSnackBar(String message) {
    showSnackBar(message: 'ℹ️ $message');
  }

  // Form validation helpers
  void showValidationError(String message) {
    showErrorSnackBar('Validation Error: $message');
  }

  void showNetworkError([String? customMessage]) {
    showErrorSnackBar(
      customMessage ?? 'Network error. Please check your connection.',
    );
  }

  void showUnauthorizedError() {
    showErrorSnackBar('You are not authorized to perform this action.');
  }

  void showServerError() {
    showErrorSnackBar('Server error. Please try again later.');
  }

  // Feature-specific navigation helpers
  Future<T?> pushToHouseDetails<T extends Object?>(String houseId) {
    return pushNamed<T>('/house/details', arguments: {'houseId': houseId});
  }

  Future<T?> pushToBooking<T extends Object?>(String houseId) {
    return pushNamed<T>('/booking', arguments: {'houseId': houseId});
  }

  Future<T?> pushToChat<T extends Object?>(String conversationId) {
    return pushNamed<T>('/chat', arguments: {'conversationId': conversationId});
  }

  Future<T?> pushToProfile<T extends Object?>() {
    return pushNamed<T>('/profile');
  }

  Future<T?> pushToSettings<T extends Object?>() {
    return pushNamed<T>('/settings');
  }

  Future<T?> pushToSearch<T extends Object?>({Map<String, dynamic>? filters}) {
    return pushNamed<T>('/search', arguments: filters);
  }

  Future<T?> pushToMyHouses<T extends Object?>() {
    return pushNamed<T>('/my-houses');
  }

  Future<T?> pushToMyBookings<T extends Object?>() {
    return pushNamed<T>('/my-bookings');
  }

  Future<T?> pushToPayments<T extends Object?>() {
    return pushNamed<T>('/payments');
  }

  Future<T?> pushToReviews<T extends Object?>(String houseId) {
    return pushNamed<T>('/reviews', arguments: {'houseId': houseId});
  }
}