class HotelModel {
  final String id;
  final String name;
  final String location;
  final double price;
  final double rating;
  final int beds;
  final int baths;
  final int guests;
  final String imageUrl;
  final bool isFavorite;
  final String category;

  const HotelModel({
    required this.id,
    required this.name,
    required this.location,
    required this.price,
    required this.rating,
    required this.beds,
    required this.baths,
    required this.guests,
    required this.imageUrl,
    this.isFavorite = false,
    required this.category,
  });

  HotelModel copyWith({
    String? id,
    String? name,
    String? location,
    double? price,
    double? rating,
    int? beds,
    int? baths,
    int? guests,
    String? imageUrl,
    bool? isFavorite,
    String? category,
  }) {
    return HotelModel(
      id: id ?? this.id,
      name: name ?? this.name,
      location: location ?? this.location,
      price: price ?? this.price,
      rating: rating ?? this.rating,
      beds: beds ?? this.beds,
      baths: baths ?? this.baths,
      guests: guests ?? this.guests,
      imageUrl: imageUrl ?? this.imageUrl,
      isFavorite: isFavorite ?? this.isFavorite,
      category: category ?? this.category,
    );
  }
}
