import 'package:flutter/material.dart';

extension ContextExtensions on BuildContext {
  // Theme access
  ThemeData get theme => Theme.of(this);
  ColorScheme get colorScheme => theme.colorScheme;
  TextTheme get textTheme => theme.textTheme;
  
  // Media query access
  MediaQueryData get mediaQuery => MediaQuery.of(this);
  Size get screenSize => mediaQuery.size;
  double get screenWidth => screenSize.width;
  double get screenHeight => screenSize.height;
  EdgeInsets get padding => mediaQuery.padding;
  EdgeInsets get viewInsets => mediaQuery.viewInsets;
  EdgeInsets get viewPadding => mediaQuery.viewPadding;
  
  // Screen type helpers
  bool get isMobile => screenWidth < 600;
  bool get isTablet => screenWidth >= 600 && screenWidth < 1200;
  bool get isDesktop => screenWidth >= 1200;
  
  // Orientation helpers
  bool get isPortrait => mediaQuery.orientation == Orientation.portrait;
  bool get isLandscape => mediaQuery.orientation == Orientation.landscape;
  
  // Responsive breakpoints
  bool get isSmallScreen => screenWidth <= 480;
  bool get isMediumScreen => screenWidth > 480 && screenWidth <= 768;
  bool get isLargeScreen => screenWidth > 768 && screenWidth <= 1024;
  bool get isExtraLargeScreen => screenWidth > 1024;
  
  // Safe area helpers
  double get topPadding => padding.top;
  double get bottomPadding => padding.bottom;
  double get statusBarHeight => topPadding;
  double get keyboardHeight => viewInsets.bottom;
  
  // Navigation helpers
  NavigatorState get navigator => Navigator.of(this);
  
  void pop<T>([T? result]) => navigator.pop<T>(result);
  
  Future<T?> pushNamed<T>(String routeName, {Object? arguments}) =>
      navigator.pushNamed<T>(routeName, arguments: arguments);
      
  Future<T?> pushReplacementNamed<T, TO>(String routeName, {Object? arguments}) =>
      navigator.pushReplacementNamed<T, TO>(routeName, arguments: arguments);
      
  Future<T?> pushNamedAndRemoveUntil<T>(String routeName, RoutePredicate predicate, {Object? arguments}) =>
      navigator.pushNamedAndRemoveUntil<T>(routeName, predicate, arguments: arguments);
  
  // Scaffold helpers
  ScaffoldState get scaffold => Scaffold.of(this);
  ScaffoldMessengerState get scaffoldMessenger => ScaffoldMessenger.of(this);
  
  void showSnackBar(SnackBar snackBar) => scaffoldMessenger.showSnackBar(snackBar);
  
  void hideCurrentSnackBar() => scaffoldMessenger.hideCurrentSnackBar();
  
  // Focus helpers
  FocusNode get focusScope => FocusScope.of(this);
  
  void unfocus() => focusScope.unfocus();
  
  void requestFocus(FocusNode focusNode) => FocusScope.of(this).requestFocus(focusNode);
  
  // Localization helpers (if using flutter_localizations)
  // Locale get locale => Localizations.localeOf(this);
  
  // Form helpers
  FormState? get form => Form.maybeOf(this);
  
  bool get hasForm => form != null;
  
  bool validateForm() => form?.validate() ?? false;
  
  void saveForm() => form?.save();
  
  void resetForm() => form?.reset();
  
  // Dialog helpers
  Future<T?> showAlertDialog<T>({
    required String title,
    required String content,
    String? confirmText,
    String? cancelText,
    VoidCallback? onConfirm,
    VoidCallback? onCancel,
  }) {
    return showDialog<T>(
      context: this,
      builder: (context) => AlertDialog(
        title: Text(title),
        content: Text(content),
        actions: [
          if (cancelText != null)
            TextButton(
              onPressed: onCancel ?? () => Navigator.of(context).pop(),
              child: Text(cancelText),
            ),
          if (confirmText != null)
            TextButton(
              onPressed: onConfirm ?? () => Navigator.of(context).pop(true),
              child: Text(confirmText),
            ),
        ],
      ),
    );
  }
  
  Future<bool?> showConfirmDialog({
    required String title,
    required String content,
    String confirmText = 'Confirm',
    String cancelText = 'Cancel',
  }) {
    return showAlertDialog<bool>(
      title: title,
      content: content,
      confirmText: confirmText,
      cancelText: cancelText,
      onConfirm: () => Navigator.of(this).pop(true),
      onCancel: () => Navigator.of(this).pop(false),
    );
  }
  
  // Bottom sheet helpers
  Future<T?> showCustomBottomSheet<T>({
    required Widget child,
    bool isScrollControlled = false,
    bool isDismissible = true,
    bool enableDrag = true,
    Color? backgroundColor,
    double? elevation,
    ShapeBorder? shape,
  }) {
    return showModalBottomSheet<T>(
      context: this,
      builder: (context) => child,
      isScrollControlled: isScrollControlled,
      isDismissible: isDismissible,
      enableDrag: enableDrag,
      backgroundColor: backgroundColor,
      elevation: elevation,
      shape: shape,
    );
  }
  
  // Responsive value selection
  T responsive<T>({
    required T mobile,
    T? tablet,
    T? desktop,
  }) {
    if (isDesktop && desktop != null) return desktop;
    if (isTablet && tablet != null) return tablet;
    return mobile;
  }
  
  // Responsive padding/margin
  EdgeInsets get responsivePadding {
    if (isMobile) return const EdgeInsets.all(16);
    if (isTablet) return const EdgeInsets.all(24);
    return const EdgeInsets.all(32);
  }
  
  EdgeInsets get responsiveHorizontalPadding {
    if (isMobile) return const EdgeInsets.symmetric(horizontal: 16);
    if (isTablet) return const EdgeInsets.symmetric(horizontal: 32);
    return const EdgeInsets.symmetric(horizontal: 48);
  }
  
  EdgeInsets get responsiveVerticalPadding {
    if (isMobile) return const EdgeInsets.symmetric(vertical: 8);
    if (isTablet) return const EdgeInsets.symmetric(vertical: 12);
    return const EdgeInsets.symmetric(vertical: 16);
  }
  
  // Responsive font sizes
  double get responsiveTitleSize {
    if (isMobile) return 20;
    if (isTablet) return 24;
    return 28;
  }
  
  double get responsiveBodySize {
    if (isMobile) return 14;
    if (isTablet) return 16;
    return 18;
  }
  
  // Quick access to common colors
  Color get primaryColor => colorScheme.primary;
  Color get secondaryColor => colorScheme.secondary;
  Color get backgroundColor => colorScheme.surface;
  Color get surfaceColor => colorScheme.surface;
  Color get errorColor => colorScheme.error;
  Color get onPrimaryColor => colorScheme.onPrimary;
  Color get onSecondaryColor => colorScheme.onSecondary;
  Color get onBackgroundColor => colorScheme.onSurface;
  Color get onSurfaceColor => colorScheme.onSurface;
  Color get onErrorColor => colorScheme.onError;
  
  // Quick access to common text styles
  TextStyle? get displayLarge => textTheme.displayLarge;
  TextStyle? get displayMedium => textTheme.displayMedium;
  TextStyle? get displaySmall => textTheme.displaySmall;
  TextStyle? get headlineLarge => textTheme.headlineLarge;
  TextStyle? get headlineMedium => textTheme.headlineMedium;
  TextStyle? get headlineSmall => textTheme.headlineSmall;
  TextStyle? get titleLarge => textTheme.titleLarge;
  TextStyle? get titleMedium => textTheme.titleMedium;
  TextStyle? get titleSmall => textTheme.titleSmall;
  TextStyle? get bodyLarge => textTheme.bodyLarge;
  TextStyle? get bodyMedium => textTheme.bodyMedium;
  TextStyle? get bodySmall => textTheme.bodySmall;
  TextStyle? get labelLarge => textTheme.labelLarge;
  TextStyle? get labelMedium => textTheme.labelMedium;
  TextStyle? get labelSmall => textTheme.labelSmall;
}