import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:provider/provider.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../../../../core/helpers/validators.dart';
import '../../../../core/utils/logger.dart';
import '../../data/models/register_request_model.dart';
import '../../data/models/user_model.dart';
import '../providers/auth_provider.dart';
import '../widgets/auth_button.dart';
import '../widgets/auth_text_field.dart';
import '../widgets/role_dropdown.dart';

class RegisterScreen extends StatefulWidget {
  const RegisterScreen({super.key});

  @override
  State<RegisterScreen> createState() => _RegisterScreenState();
}

class _RegisterScreenState extends State<RegisterScreen> {
  //Fields controller logic 
  final _formKey = GlobalKey<FormState>();
  final _phoneController = TextEditingController();
  final _passwordController = TextEditingController();
  final _confirmPasswordController = TextEditingController();
  final _firstNameController = TextEditingController();
  final _lastNameController = TextEditingController();
  final _emailController = TextEditingController();
  
  UserRole? _selectedRole;
  final OtpDeliveryMethod _otpMethod = OtpDeliveryMethod.email; // Email only
  bool _acceptTerms = false;

  @override
  void dispose() {
    _phoneController.dispose();
    _passwordController.dispose();
    _confirmPasswordController.dispose();
    _firstNameController.dispose();
    _lastNameController.dispose();
    _emailController.dispose();
    super.dispose();
  }

  void _register() async {
    print('=== REGISTER SCREEN: REGISTRATION ATTEMPT START ===');
    Logger.info('=== REGISTER SCREEN: REGISTRATION ATTEMPT START ===');

    if (!_formKey.currentState!.validate()) {
      print('Form validation failed');
      Logger.warning('Form validation failed');
      return;
    }
    if (!_acceptTerms) {
      print('Terms not accepted');
      Logger.warning('Terms not accepted');
      _showSnackBar('Please accept the terms and conditions');
      return;
    }
    if (_selectedRole == null) {
      print('No role selected');
      Logger.warning('No role selected');
      _showSnackBar('Please select your role');
      return;
    }

    print('Creating registration request with:');
    print('Phone: ${_phoneController.text.trim()}');
    print('First Name: ${_firstNameController.text.trim()}');
    print('Last Name: ${_lastNameController.text.trim()}');
    print('Email: ${_emailController.text.trim()}');
    print('Role: $_selectedRole');
    print('OTP Method: $_otpMethod');

    Logger.info('Creating registration request with:');
    Logger.info('Phone: ${_phoneController.text.trim()}');
    Logger.info('First Name: ${_firstNameController.text.trim()}');
    Logger.info('Last Name: ${_lastNameController.text.trim()}');
    Logger.info('Email: ${_emailController.text.trim()}');
    Logger.info('Role: $_selectedRole');
    Logger.info('OTP Method: $_otpMethod');
    
    //assigning in register modal
    final request = RegisterRequestModel(
      phoneNumber: _phoneController.text.trim(),
      password: _passwordController.text,
      confirmPassword: _confirmPasswordController.text,
      firstName: _firstNameController.text.trim(),
      lastName: _lastNameController.text.trim(),
      email: _emailController.text.trim(),
      role: _selectedRole!,
      otpMethod: _otpMethod,
    );

    print('Request object created: ${request.toString()}');
    print('Request JSON: ${request.toJson()}');
    Logger.info('Request object created: ${request.toString()}');
    Logger.info('Request JSON: ${request.toJson()}');

    final authProvider = context.read<AuthProvider>();
    print('Calling authProvider.register()');
    Logger.info('Calling authProvider.register()');

    final success = await authProvider.register(request);

    print('Registration result: $success');
    Logger.info('Registration result: $success');

    if (success) {
      print('Registration successful, navigating to OTP verification');
      Logger.info('Registration successful, navigating to OTP verification');
      if (mounted) {
        Navigator.pushNamed(context, '/otp-verification');
      }
    } else if (authProvider.errorMessage != null) {
      print('Registration failed');
      print('Error message: ${authProvider.errorMessage}');
      Logger.error('Registration failed');
      Logger.error('Error message: ${authProvider.errorMessage}');
      _showSnackBar(authProvider.errorMessage!);
    }

    print('=== REGISTER SCREEN: REGISTRATION ATTEMPT END ===');
    Logger.info('=== REGISTER SCREEN: REGISTRATION ATTEMPT END ===');
  }

  void _showSnackBar(String message) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: AppColors.error,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(16),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(8),
        ),
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.white,
      appBar: AppBar(
        backgroundColor: AppColors.primary,
        elevation: 0,
        leading: IconButton(
          icon: const Icon(
            Icons.arrow_back,
            color: Colors.white,
          ),
          onPressed: () => context.go('/login'),
        ),
        title: Text(
          'Register',
          style: AppTextStyles.headlineSmall.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        centerTitle: true,
        systemOverlayStyle: const SystemUiOverlayStyle(
          statusBarColor: AppColors.primary,
          statusBarIconBrightness: Brightness.light,
        ),
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Header
                _buildHeader(),
                const SizedBox(height: 32),

                // Form Fields
                _buildFormFields(),
                const SizedBox(height: 24),

                // Terms and Conditions
                _buildTermsCheckbox(),
                const SizedBox(height: 32),

                // Register Button
                _buildRegisterButton(),
                const SizedBox(height: 24),

                // Login Link
                _buildLoginLink(),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildHeader() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Getting Started',
          style: AppTextStyles.headlineLarge.copyWith(
            fontWeight: FontWeight.bold,
            color: Colors.black,
            fontSize: 28,
          ),
        ),
        const SizedBox(height: 8),
        Text(
          'Seems you are new here. Let\'s set up your profile',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.textSecondary,
            fontSize: 16,
          ),
        ),
      ],
    );
  }

  Widget _buildFormFields() {
    return Column(
      children: [
        // 1. First Name
        AuthTextField(
          label: 'First name',
          hint: 'john',
          controller: _firstNameController,
          validator: (value) => Validators.name(value),
          keyboardType: TextInputType.name,
        ),
        const SizedBox(height: 20),

        // 2. Second Name (Last Name)
        AuthTextField(
          label: 'Second name',
          hint: 'doe',
          controller: _lastNameController,
          validator: (value) => Validators.name(value),
          keyboardType: TextInputType.name,
        ),
        const SizedBox(height: 20),

        // 3. Phone Number
        PhoneTextField(
          label: 'Phone number',
          hint: 'Phone number',
          controller: _phoneController,
          validator: Validators.phoneNumber,
        ),
        const SizedBox(height: 20),

        // 4. Role
        RoleDropdown(
          label: 'Role',
          value: _selectedRole,
          onChanged: (role) {
            setState(() {
              _selectedRole = role;
            });
          },
        ),
        const SizedBox(height: 20),

        // 5. Email Address
        AuthTextField(
          label: 'Email Address',
          hint: '<EMAIL>',
          controller: _emailController,
          validator: Validators.email,
          keyboardType: TextInputType.emailAddress,
        ),
        const SizedBox(height: 20),

        // 6. Password
        PasswordTextField(
          label: 'Password',
          hint: 'Password',
          controller: _passwordController,
          validator: Validators.password,
        ),
        const SizedBox(height: 20),

        // 7. Confirm Password
        PasswordTextField(
          label: 'Confirm Password',
          hint: 'Password',
          controller: _confirmPasswordController,
          validator: (value) => Validators.confirmPassword(
            value,
            _passwordController.text,
          ),
        ),
        // Note: OTP will be sent via email
        // Container(
        //   padding: const EdgeInsets.all(12),
        //   decoration: BoxDecoration(
        //     color: AppColors.primary.withValues(alpha: 0.1),
        //     borderRadius: BorderRadius.circular(8),
        //     border: Border.all(color: AppColors.primary.withValues(alpha: 0.3)),
        //   ),
        //   child: Row(
        //     children: [
        //       const Icon(
        //         Icons.email_outlined,
        //         color: AppColors.primary,
        //         size: 20,
        //       ),
        //       const SizedBox(width: 8),
        //       Text(
        //         'OTP verification will be sent to your email',
        //         style: AppTextStyles.bodySmall.copyWith(
        //           color: AppColors.primary,
        //           fontWeight: FontWeight.w500,
        //         ),
        //       ),
        //     ],
        //   ),
        // ),
      ],
    );
  }

  Widget _buildTermsCheckbox() {
    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Checkbox(
          value: _acceptTerms,
          onChanged: (value) {
            setState(() {
              _acceptTerms = value ?? false;
            });
          },
          activeColor: AppColors.primary,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        Expanded(
          child: GestureDetector(
            onTap: () {
              setState(() {
                _acceptTerms = !_acceptTerms;
              });
            },
            child: Padding(
              padding: const EdgeInsets.only(top: 12),
              child: RichText(
                text: TextSpan(
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.textSecondary,
                  ),
                  children: [
                    const TextSpan(text: 'By creating an account, you agree to our '),
                    TextSpan(
                      text: 'Terms and Conditions',
                      style: AppTextStyles.bodyMedium.copyWith(
                        color: AppColors.primary,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildRegisterButton() {
    return Consumer<AuthProvider>(
      builder: (context, authProvider, child) {
        return AuthButton(
          text: 'Continue',
          onPressed: _register,
          isLoading: authProvider.isLoading,
          width: double.infinity,
        );
      },
    );
  }

  Widget _buildLoginLink() {
    return Center(
      child: RichText(
        text: TextSpan(
          style: AppTextStyles.bodyMedium.copyWith(
            color: AppColors.textSecondary,
          ),
          children: [
            const TextSpan(text: 'Already have an account? '),
            WidgetSpan(
              child: GestureDetector(
                onTap: () {
                  context.go('/login');
                },
                child: Text(
                  'Login',
                  style: AppTextStyles.bodyMedium.copyWith(
                    color: AppColors.primary,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
