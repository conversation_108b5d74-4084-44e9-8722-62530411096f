import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';
import '../constants/app_constants.dart';

class StorageService {
  static StorageService? _instance;
  static SharedPreferences? _prefs;

  StorageService._();

  static StorageService get instance {
    _instance ??= StorageService._();
    return _instance!;
  }

  static Future<void> init() async {
    _prefs = await SharedPreferences.getInstance();
  }

  // String operations
  Future<void> setString(String key, String value) async {
    await _prefs?.setString(key, value);
  }

  String? getString(String key) {
    return _prefs?.getString(key);
  }

  // Int operations
  Future<void> setInt(String key, int value) async {
    await _prefs?.setInt(key, value);
  }

  int? getInt(String key) {
    return _prefs?.getInt(key);
  }

  // Bool operations
  Future<void> setBool(String key, bool value) async {
    await _prefs?.setBool(key, value);
  }

  bool? getBool(String key) {
    return _prefs?.getBool(key);
  }

  // Double operations
  Future<void> setDouble(String key, double value) async {
    await _prefs?.setDouble(key, value);
  }

  double? getDouble(String key) {
    return _prefs?.getDouble(key);
  }

  // List<String> operations
  Future<void> setStringList(String key, List<String> value) async {
    await _prefs?.setStringList(key, value);
  }

  List<String>? getStringList(String key) {
    return _prefs?.getStringList(key);
  }

  // Object operations (using JSON)
  Future<void> setObject(String key, Map<String, dynamic> value) async {
    final jsonString = jsonEncode(value);
    await setString(key, jsonString);
  }

  Map<String, dynamic>? getObject(String key) {
    final jsonString = getString(key);
    if (jsonString == null) return null;
    
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  // Remove operations
  Future<void> remove(String key) async {
    await _prefs?.remove(key);
  }

  Future<void> clear() async {
    await _prefs?.clear();
  }

  bool containsKey(String key) {
    return _prefs?.containsKey(key) ?? false;
  }

  Set<String> getKeys() {
    return _prefs?.getKeys() ?? <String>{};
  }

  // Auth-related methods
  Future<void> setAuthToken(String token) async {
    await setString(AppConstants.keyAuthToken, token);
  }

  String? getAuthToken() {
    return getString(AppConstants.keyAuthToken);
  }

  Future<void> setRefreshToken(String token) async {
    await setString(AppConstants.keyRefreshToken, token);
  }

  String? getRefreshToken() {
    return getString(AppConstants.keyRefreshToken);
  }

  Future<void> setUserData(Map<String, dynamic> userData) async {
    await setObject(AppConstants.keyUserData, userData);
  }

  Map<String, dynamic>? getUserData() {
    return getObject(AppConstants.keyUserData);
  }

  Future<void> setUserRole(String role) async {
    await setString(AppConstants.keyUserRole, role);
  }

  String? getUserRole() {
    return getString(AppConstants.keyUserRole);
  }

  Future<void> setOnboardingComplete(bool isComplete) async {
    await setBool(AppConstants.keyOnboardingComplete, isComplete);
  }

  bool isOnboardingComplete() {
    return getBool(AppConstants.keyOnboardingComplete) ?? false;
  }

  Future<void> setThemeMode(String themeMode) async {
    await setString(AppConstants.keyThemeMode, themeMode);
  }

  String? getThemeMode() {
    return getString(AppConstants.keyThemeMode);
  }

  Future<void> setLanguage(String language) async {
    await setString(AppConstants.keyLanguage, language);
  }

  String? getLanguage() {
    return getString(AppConstants.keyLanguage);
  }

  // Clear auth data
  Future<void> clearAuthData() async {
    await remove(AppConstants.keyAuthToken);
    await remove(AppConstants.keyRefreshToken);
    await remove(AppConstants.keyUserData);
    await remove(AppConstants.keyUserRole);
  }

  // Check if user is logged in
  bool isLoggedIn() {
    final token = getAuthToken();
    return token != null && token.isNotEmpty;
  }
}