import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../../../authentication/presentation/widgets/auth_button.dart';

class BookingScreen extends StatelessWidget {
  final bool isAuthenticated;

  const BookingScreen({
    super.key,
    required this.isAuthenticated,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        title: Text(
          'Your Bookings',
          style: AppTextStyles.headlineSmall.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: isAuthenticated ? _buildAuthenticatedView() : _buildUnauthenticatedView(context),
    );
  }

  Widget _buildUnauthenticatedView(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Illustration/Icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: AppColors.primary.withValues(alpha: 0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.calendar_today_outlined,
              size: 60,
              color: AppColors.primary,
            ),
          ),
          const SizedBox(height: 32),

          // Title
          Text(
            'Log In to Start and View your bookings',
            style: AppTextStyles.headlineSmall.copyWith(
              color: AppColors.textPrimary,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 16),

          // Description
          Text(
            'You can Create, View or edit your booking once Log In.',
            style: AppTextStyles.bodyLarge.copyWith(
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 40),

          // Login Button
          AuthButton(
            text: 'Log In',
            onPressed: () {
              context.go('/login');
            },
            backgroundColor: AppColors.primary,
            width: double.infinity,
            icon: Icons.login,
          ),
        ],
      ),
    );
  }

  Widget _buildAuthenticatedView() {
    // TODO: Implement authenticated booking view
    return const Center(
      child: Text('Authenticated Booking View - To be implemented'),
    );
  }
}
