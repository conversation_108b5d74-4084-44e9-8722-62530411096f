import 'package:flutter/material.dart';

class AppColors {
  // Primary Colors (Blue Theme)
  static const Color primary = Color(0xFF1565C0); // Deep Blue
  static const Color primaryLight = Color(0xFF5E92F3); // Light Blue
  static const Color primaryDark = Color(0xFF003C8F); // Dark Blue
  
  // Secondary Colors
  static const Color secondary = Color(0xFF0D47A1); // Blue Secondary
  static const Color secondaryLight = Color(0xFF5472D3); // Light Blue Secondary
  static const Color secondaryDark = Color(0xFF002171); // Dark Blue Secondary
  
  // Accent Colors
  static const Color accent = Color(0xFF2196F3); // Material Blue
  static const Color accentLight = Color(0xFF6EC6FF); // Light Accent
  static const Color accentDark = Color(0xFF0069C0); // Dark Accent
  
  // Background Colors
  static const Color background = Color(0xFFFFFFFF); // Pure White
  static const Color backgroundLight = Color(0xFFF8F9FA); // Off White
  static const Color backgroundDark = Color(0xFFF5F5F5); // Light Grey
  
  // Surface Colors
  static const Color surface = Color(0xFFFFFFFF); // White
  static const Color surfaceVariant = Color(0xFFF3F4F6); // Light Grey
  static const Color surfaceContainer = Color(0xFFFAFAFA); // Very Light Grey
  
  // Text Colors
  static const Color textPrimary = Color(0xFF212121); // Dark Grey
  static const Color textSecondary = Color(0xFF757575); // Medium Grey
  static const Color textTertiary = Color(0xFF9E9E9E); // Light Grey
  static const Color textDisabled = Color(0xFFBDBDBD); // Disabled Grey
  static const Color textOnPrimary = Color(0xFFFFFFFF); // White on Blue
  
  // Status Colors
  static const Color success = Color(0xFF4CAF50); // Green
  static const Color successLight = Color(0xFF81C784); // Light Green
  static const Color warning = Color(0xFFFF9800); // Orange
  static const Color warningLight = Color(0xFFFFB74D); // Light Orange
  static const Color error = Color(0xFFF44336); // Red
  static const Color errorLight = Color(0xFFE57373); // Light Red
  static const Color info = Color(0xFF2196F3); // Blue (same as accent)
  
  // Border Colors
  static const Color border = Color(0xFFE0E0E0); // Light Grey Border
  static const Color borderLight = Color(0xFFF0F0F0); // Very Light Border
  static const Color borderDark = Color(0xFFBDBDBD); // Dark Border
  
  // Shadow Colors
  static const Color shadow = Color(0x1A000000); // 10% Black
  static const Color shadowLight = Color(0x0D000000); // 5% Black
  static const Color shadowDark = Color(0x26000000); // 15% Black
  
  // Overlay Colors
  static const Color overlay = Color(0x80000000); // 50% Black
  static const Color overlayLight = Color(0x40000000); // 25% Black
  static const Color overlayDark = Color(0xB3000000); // 70% Black
  
  // Shimmer Colors (for loading effects)
  static const Color shimmerBase = Color(0xFFE0E0E0);
  static const Color shimmerHighlight = Color(0xFFF5F5F5);
  
  // Card Colors
  static const Color cardBackground = Color(0xFFFFFFFF); // White
  static const Color cardElevated = Color(0xFFFAFAFA); // Very Light Grey
  
  // Input Colors
  static const Color inputBackground = Color(0xFFF8F9FA); // Off White
  static const Color inputBorder = Color(0xFFE0E0E0); // Light Grey
  static const Color inputFocused = Color(0xFF1565C0); // Primary Blue
  
  // Button Colors
  static const Color buttonPrimary = Color(0xFF1565C0); // Primary Blue
  static const Color buttonSecondary = Color(0xFFE3F2FD); // Very Light Blue
  static const Color buttonDisabled = Color(0xFFE0E0E0); // Light Grey
  
  // Rating Colors
  static const Color ratingFilled = Color(0xFFFFC107); // Amber
  static const Color ratingEmpty = Color(0xFFE0E0E0); // Light Grey
  
  // Gradient Colors
  static const List<Color> primaryGradient = [
    Color(0xFF1565C0), // Primary
    Color(0xFF1976D2), // Slightly lighter
  ];
  
  static const List<Color> backgroundGradient = [
    Color(0xFFFFFFFF), // White
    Color(0xFFF8F9FA), // Off White
  ];
  
  // Feature-specific Colors
  static const Color bookingConfirmed = Color(0xFF4CAF50); // Green
  static const Color bookingPending = Color(0xFFFF9800); // Orange
  static const Color bookingCancelled = Color(0xFFF44336); // Red
  
  static const Color paymentSuccess = Color(0xFF4CAF50); // Green
  static const Color paymentPending = Color(0xFFFF9800); // Orange
  static const Color paymentFailed = Color(0xFFF44336); // Red
  
  static const Color houseFeatured = Color(0xFFFFD700); // Gold
  static const Color houseAvailable = Color(0xFF4CAF50); // Green
  static const Color houseBooked = Color(0xFFFF9800); // Orange
  static const Color houseUnavailable = Color(0xFFF44336); // Red
  
  // Chat Colors
  static const Color chatSent = Color(0xFF1565C0); // Primary Blue
  static const Color chatReceived = Color(0xFFF0F0F0); // Light Grey
  static const Color chatOnline = Color(0xFF4CAF50); // Green
  static const Color chatOffline = Color(0xFF9E9E9E); // Grey
}