// This is a basic Flutter widget test for EMakazi app.

import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';

import 'package:emakazi_v2/app/app.dart';

void main() {
  testWidgets('EMakazi app smoke test', (WidgetTester tester) async {
    // Build our app and trigger a frame.
    await tester.pumpWidget(const EmakaziApp());

    // Verify that the app loads without crashing
    expect(find.byType(MaterialApp), findsOneWidget);
  });
}
