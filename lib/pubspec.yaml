name: emakazi_v2
description: EMakazi - House Rental and Booking Platform

publish_to: 'none' # Remove this line if you wish to publish to pub.dev

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter

  # HTTP & Networking (for API calls)
  dio: ^5.3.2

  # State Management (choose one)
  provider: ^6.1.1

  # Local Storage (for session/token storage)
  shared_preferences: ^2.2.2

  # Navigation
  go_router: ^12.1.3

  # Date/Time Utilities (for your date extensions)
  intl: ^0.18.1

  # JSON Serialization (for API responses)
  json_annotation: ^4.8.1

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation (for JSON serialization)
  build_runner: ^2.4.7
  json_serializable: ^6.7.1

  # Linting
  flutter_lints: ^3.0.1

flutter:
  uses-material-design: true

  # Assets configuration - commented out until we add actual assets
  # assets:
  #   - assets/images/
  #   - assets/icons/
  #   - assets/animations/

  # Fonts configuration - commented out until we add actual fonts
  # fonts:
  #   - family: Inter
  #     fonts:
  #       - asset: fonts/Inter-Regular.ttf
  #       - asset: fonts/Inter-Medium.ttf
  #         weight: 500
  #       - asset: fonts/Inter-SemiBold.ttf
  #         weight: 600
  #       - asset: fonts/Inter-Bold.ttf
  #         weight: 700
