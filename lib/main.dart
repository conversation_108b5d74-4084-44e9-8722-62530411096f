import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'app/app.dart';
import 'core/services/storage_service.dart';
import 'core/services/session_service.dart';
import 'core/utils/logger.dart';

void main() async {
  // Ensure Flutter binding is initialized
  WidgetsFlutterBinding.ensureInitialized();
  
  // Configure system UI
  await _configureSystemUI();
  
  // Initialize core services
  await _initializeServices();
  
  // Run the app
  runApp(const EmakaziApp());
}

Future<void> _configureSystemUI() async {
  try {
    // Set preferred orientations
    await SystemChrome.setPreferredOrientations([
      DeviceOrientation.portraitUp,
      DeviceOrientation.portraitDown,
    ]);
    
    // Configure system overlay style
    SystemChrome.setSystemUIOverlayStyle(
      const SystemUiOverlayStyle(
        statusBarColor: Colors.transparent,
        statusBarIconBrightness: Brightness.light,
        systemNavigationBarColor: Colors.white,
        systemNavigationBarIconBrightness: Brightness.dark,
      ),
    );
    
    Logger.info('System UI configured successfully');
  } catch (e) {
    Logger.error('Failed to configure system UI', e);
  }
}

Future<void> _initializeServices() async {
  try {
    Logger.info('Initializing EMakazi app services...');
    
    // Initialize storage service
    await StorageService.init();
    Logger.info('Storage service initialized');
    
    // Initialize session service
    await SessionService.instance.initialize();
    Logger.info('Session service initialized');
    
    // TODO: Initialize other services as needed
    // - Notification service
    // - Analytics service
    // - Crash reporting service
    // - Location service
    // - Network connectivity service
    
    Logger.info('All core services initialized successfully');
  } catch (e, stackTrace) {
    Logger.error('Failed to initialize services', e, stackTrace);
    // Continue app execution even if some services fail
    // We can show appropriate error messages in the UI
  }
}
