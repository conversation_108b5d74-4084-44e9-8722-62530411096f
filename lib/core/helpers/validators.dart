import '../extensions/string_extensions.dart';

class Validators {
  // Email validation
  static String? email(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    if (!value.isEmail) {
      return 'Please enter a valid email address';
    }
    return null;
  }
  
  // Password validation
  static String? password(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 8) {
      return 'Password must be at least 8 characters long';
    }
    if (!value.isStrongPassword) {
      return 'Password must contain uppercase, lowercase, number and special character';
    }
    return null;
  }
  
  // Simple password validation (for login)
  static String? simplePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    if (value.length < 6) {
      return 'Password must be at least 6 characters long';
    }
    return null;
  }
  
  // Confirm password validation
  static String? confirmPassword(String? value, String? originalPassword) {
    if (value == null || value.isEmpty) {
      return 'Please confirm your password';
    }
    if (value != originalPassword) {
      return 'Passwords do not match';
    }
    return null;
  }
  
  // Phone number validation
  static String? phoneNumber(String? value) {
    if (value == null || value.isEmpty) {
      return 'Phone number is required';
    }
    
    // Remove spaces and formatting
    final cleanPhone = value.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    if (!cleanPhone.isPhoneNumber) {
      return 'Please enter a valid phone number';
    }
    
    // Tanzania specific validation
    if (cleanPhone.startsWith('+255') && cleanPhone.length != 13) {
      return 'Tanzania phone number should be 12 digits after +255';
    } else if (cleanPhone.startsWith('0') && cleanPhone.length != 10) {
      return 'Phone number should be 10 digits starting with 0';
    } else if (cleanPhone.startsWith('255') && cleanPhone.length != 12) {
      return 'Phone number should be 12 digits total';
    }
    
    return null;
  }
  
  // Name validation
  static String? name(String? value) {
    if (value == null || value.isEmpty) {
      return 'Name is required';
    }
    if (value.trim().length < 2) {
      return 'Name must be at least 2 characters long';
    }
    if (value.trim().length > 50) {
      return 'Name cannot be more than 50 characters long';
    }
    final nameRegex = RegExp(r'^[a-zA-Z\s\-\.]+$');
    if (!nameRegex.hasMatch(value.trim())) {
      return 'Name can only contain letters, spaces, hyphens and dots';
    }
    return null;
  }
  
  // Required field validation
  static String? required(String? value, [String fieldName = 'This field']) {
    if (value == null || value.trim().isEmpty) {
      return '$fieldName is required';
    }
    return null;
  }
  
  // Minimum length validation
  static String? minLength(String? value, int minLength, [String fieldName = 'This field']) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    if (value.length < minLength) {
      return '$fieldName must be at least $minLength characters long';
    }
    return null;
  }
  
  // Maximum length validation
  static String? maxLength(String? value, int maxLength, [String fieldName = 'This field']) {
    if (value != null && value.length > maxLength) {
      return '$fieldName cannot be more than $maxLength characters long';
    }
    return null;
  }
  
  // Numeric validation
  static String? numeric(String? value, [String fieldName = 'This field']) {
    if (value == null || value.isEmpty) {
      return '$fieldName is required';
    }
    if (!value.isNumeric) {
      return '$fieldName must be a number';
    }
    return null;
  }
  
  // Price validation
  static String? price(String? value) {
    if (value == null || value.isEmpty) {
      return 'Price is required';
    }
    
    final price = double.tryParse(value.replaceAll(',', ''));
    if (price == null) {
      return 'Please enter a valid price';
    }
    if (price < 0) {
      return 'Price cannot be negative';
    }
    if (price > 10000000) {
      return 'Price seems too high';
    }
    
    return null;
  }
  
  // Age validation
  static String? age(String? value) {
    if (value == null || value.isEmpty) {
      return 'Age is required';
    }
    
    final age = int.tryParse(value);
    if (age == null) {
      return 'Please enter a valid age';
    }
    if (age < 18) {
      return 'You must be at least 18 years old';
    }
    if (age > 120) {
      return 'Please enter a valid age';
    }
    
    return null;
  }
  
  // Date validation
  static String? date(String? value) {
    if (value == null || value.isEmpty) {
      return 'Date is required';
    }
    
    try {
      DateTime.parse(value);
      return null;
    } catch (e) {
      return 'Please enter a valid date';
    }
  }
  
  // Future date validation
  static String? futureDate(String? value) {
    final dateError = date(value);
    if (dateError != null) return dateError;
    
    final dateTime = DateTime.parse(value!);
    if (dateTime.isBefore(DateTime.now())) {
      return 'Date must be in the future';
    }
    
    return null;
  }
  
  // Birth date validation
  static String? birthDate(String? value) {
    final dateError = date(value);
    if (dateError != null) return dateError;
    
    final dateTime = DateTime.parse(value!);
    final now = DateTime.now();
    final age = now.year - dateTime.year;
    
    if (dateTime.isAfter(now)) {
      return 'Birth date cannot be in the future';
    }
    if (age < 18) {
      return 'You must be at least 18 years old';
    }
    if (age > 120) {
      return 'Please enter a valid birth date';
    }
    
    return null;
  }
  
  // URL validation
  static String? url(String? value) {
    if (value == null || value.isEmpty) {
      return 'URL is required';
    }
    if (!value.isUrl) {
      return 'Please enter a valid URL';
    }
    return null;
  }
  
  // Optional URL validation
  static String? optionalUrl(String? value) {
    if (value == null || value.isEmpty) {
      return null; // Optional field
    }
    return url(value);
  }
  
  // Description validation
  static String? description(String? value, {int minLength = 10, int maxLength = 500}) {
    if (value == null || value.trim().isEmpty) {
      return 'Description is required';
    }
    if (value.trim().length < minLength) {
      return 'Description must be at least $minLength characters long';
    }
    if (value.trim().length > maxLength) {
      return 'Description cannot be more than $maxLength characters long';
    }
    return null;
  }
  
  // House-specific validations
  static String? houseTitle(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'House title is required';
    }
    if (value.trim().length < 5) {
      return 'House title must be at least 5 characters long';
    }
    if (value.trim().length > 100) {
      return 'House title cannot be more than 100 characters long';
    }
    return null;
  }
  
  static String? numberOfRooms(String? value) {
    if (value == null || value.isEmpty) {
      return 'Number of rooms is required';
    }
    
    final rooms = int.tryParse(value);
    if (rooms == null) {
      return 'Please enter a valid number';
    }
    if (rooms < 1) {
      return 'House must have at least 1 room';
    }
    if (rooms > 50) {
      return 'Number of rooms seems too high';
    }
    
    return null;
  }
  
  static String? area(String? value) {
    if (value == null || value.isEmpty) {
      return 'Area is required';
    }
    
    final area = double.tryParse(value);
    if (area == null) {
      return 'Please enter a valid area';
    }
    if (area < 10) {
      return 'Area must be at least 10 square meters';
    }
    if (area > 10000) {
      return 'Area seems too large';
    }
    
    return null;
  }
  
  // Location validation
  static String? address(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'Address is required';
    }
    if (value.trim().length < 10) {
      return 'Please provide a more detailed address';
    }
    if (value.trim().length > 200) {
      return 'Address is too long';
    }
    return null;
  }
  
  static String? city(String? value) {
    if (value == null || value.trim().isEmpty) {
      return 'City is required';
    }
    if (value.trim().length < 2) {
      return 'City name is too short';
    }
    final cityRegex = RegExp(r'^[a-zA-Z\s\-]+$');
    if (!cityRegex.hasMatch(value.trim())) {
      return 'City name contains invalid characters';
    }
    return null;
  }
  
  // Credit card validation (for payments)
  static String? creditCard(String? value) {
    if (value == null || value.isEmpty) {
      return 'Card number is required';
    }
    
    final cardNumber = value.replaceAll(RegExp(r'\s'), '');
    if (!cardNumber.isNumeric) {
      return 'Card number must contain only digits';
    }
    if (cardNumber.length < 13 || cardNumber.length > 19) {
      return 'Invalid card number length';
    }
    
    // Luhn algorithm validation
    if (!_isValidLuhn(cardNumber)) {
      return 'Invalid card number';
    }
    
    return null;
  }
  
  static String? expiryDate(String? value) {
    if (value == null || value.isEmpty) {
      return 'Expiry date is required';
    }
    
    if (!RegExp(r'^\d{2}/\d{2}$').hasMatch(value)) {
      return 'Expiry date must be in MM/YY format';
    }
    
    final parts = value.split('/');
    final month = int.tryParse(parts[0]);
    final year = int.tryParse(parts[1]);
    
    if (month == null || year == null) {
      return 'Invalid expiry date';
    }
    if (month < 1 || month > 12) {
      return 'Invalid month';
    }
    
    final now = DateTime.now();
    final expiryDate = DateTime(2000 + year, month);
    if (expiryDate.isBefore(DateTime(now.year, now.month))) {
      return 'Card has expired';
    }
    
    return null;
  }
  
  static String? cvv(String? value) {
    if (value == null || value.isEmpty) {
      return 'CVV is required';
    }
    if (!RegExp(r'^\d{3,4}$').hasMatch(value)) {
      return 'CVV must be 3 or 4 digits';
    }
    return null;
  }
  
  // Custom validation combining multiple validators
  static String? Function(String?) combine(List<String? Function(String?)> validators) {
    return (String? value) {
      for (final validator in validators) {
        final result = validator(value);
        if (result != null) return result;
      }
      return null;
    };
  }
  
  // Conditional validation
  static String? Function(String?) when(
    bool condition,
    String? Function(String?) validator,
  ) {
    return (String? value) {
      if (condition) {
        return validator(value);
      }
      return null;
    };
  }
  
  // Helper method for Luhn algorithm
  // OTP validation
  static String? otp(String? value) {
    if (value == null || value.isEmpty) {
      return 'OTP is required';
    }
    if (value.length != 6) {
      return 'OTP must be 6 digits';
    }
    if (!RegExp(r'^\d{6}$').hasMatch(value)) {
      return 'OTP must contain only numbers';
    }
    return null;
  }

  static bool _isValidLuhn(String cardNumber) {
    int sum = 0;
    bool isEven = false;
    
    for (int i = cardNumber.length - 1; i >= 0; i--) {
      int digit = int.parse(cardNumber[i]);
      
      if (isEven) {
        digit *= 2;
        if (digit > 9) {
          digit = digit ~/ 10 + digit % 10;
        }
      }
      
      sum += digit;
      isEven = !isEven;
    }
    
    return sum % 10 == 0;
  }
}