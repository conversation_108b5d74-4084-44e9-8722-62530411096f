class DateHelpers {
  // Get current date without time
  static DateTime get today {
    final now = DateTime.now();
    return DateTime(now.year, now.month, now.day);
  }
  
  // Get tomorrow's date
  static DateTime get tomorrow {
    return today.add(const Duration(days: 1));
  }
  
  // Get yesterday's date
  static DateTime get yesterday {
    return today.subtract(const Duration(days: 1));
  }
  
  // Get start of current week (Monday)
  static DateTime get startOfWeek {
    final now = DateTime.now();
    return now.subtract(Duration(days: now.weekday - 1));
  }
  
  // Get end of current week (Sunday)
  static DateTime get endOfWeek {
    return startOfWeek.add(const Duration(days: 6));
  }
  
  // Get start of current month
  static DateTime get startOfMonth {
    final now = DateTime.now();
    return DateTime(now.year, now.month, 1);
  }
  
  // Get end of current month
  static DateTime get endOfMonth {
    final now = DateTime.now();
    return DateTime(now.year, now.month + 1, 0);
  }
  
  // Get start of current year
  static DateTime get startOfYear {
    final now = DateTime.now();
    return DateTime(now.year, 1, 1);
  }
  
  // Get end of current year
  static DateTime get endOfYear {
    final now = DateTime.now();
    return DateTime(now.year, 12, 31);
  }
  
  // Check if date is within a range
  static bool isInRange(DateTime date, DateTime start, DateTime end) {
    return date.isAfter(start.subtract(const Duration(days: 1))) &&
           date.isBefore(end.add(const Duration(days: 1)));
  }
  
  // Get age from birth date
  static int calculateAge(DateTime birthDate) {
    final now = DateTime.now();
    int age = now.year - birthDate.year;
    
    if (now.month < birthDate.month || 
        (now.month == birthDate.month && now.day < birthDate.day)) {
      age--;
    }
    
    return age;
  }
  
  // Get days between two dates
  static int daysBetween(DateTime date1, DateTime date2) {
    return date2.difference(date1).inDays;
  }
  
  // Get business days between two dates
  static int businessDaysBetween(DateTime start, DateTime end) {
    if (start.isAfter(end)) {
      return -businessDaysBetween(end, start);
    }
    
    int businessDays = 0;
    DateTime current = start;
    
    while (current.isBefore(end) || current.isAtSameMomentAs(end)) {
      if (current.weekday != DateTime.saturday && 
          current.weekday != DateTime.sunday) {
        businessDays++;
      }
      current = current.add(const Duration(days: 1));
    }
    
    return businessDays;
  }
  
  // Add business days to a date
  static DateTime addBusinessDays(DateTime date, int days) {
    DateTime result = date;
    int remainingDays = days;
    
    while (remainingDays > 0) {
      result = result.add(const Duration(days: 1));
      if (result.weekday != DateTime.saturday && 
          result.weekday != DateTime.sunday) {
        remainingDays--;
      }
    }
    
    return result;
  }
  
  // Check if date is a weekend
  static bool isWeekend(DateTime date) {
    return date.weekday == DateTime.saturday || 
           date.weekday == DateTime.sunday;
  }
  
  // Check if date is a weekday
  static bool isWeekday(DateTime date) {
    return !isWeekend(date);
  }
  
  // Get next business day
  static DateTime nextBusinessDay(DateTime date) {
    DateTime next = date.add(const Duration(days: 1));
    while (isWeekend(next)) {
      next = next.add(const Duration(days: 1));
    }
    return next;
  }
  
  // Get previous business day
  static DateTime previousBusinessDay(DateTime date) {
    DateTime previous = date.subtract(const Duration(days: 1));
    while (isWeekend(previous)) {
      previous = previous.subtract(const Duration(days: 1));
    }
    return previous;
  }
  
  // Parse date string with multiple formats
  static DateTime? parseDate(String dateString) {
    // Try parsing as ISO format first
    try {
      return DateTime.parse(dateString);
    } catch (e) {
      // If that fails, try other common formats
      // This is a simplified implementation
      // In a real app, you might want to use the intl package for better parsing
      return null;
    }
  }
  
  // Get quarter of the year
  static int getQuarter(DateTime date) {
    return ((date.month - 1) / 3).floor() + 1;
  }
  
  // Get start of quarter
  static DateTime getStartOfQuarter(DateTime date) {
    final quarter = getQuarter(date);
    final startMonth = ((quarter - 1) * 3) + 1;
    return DateTime(date.year, startMonth, 1);
  }
  
  // Get end of quarter
  static DateTime getEndOfQuarter(DateTime date) {
    final quarter = getQuarter(date);
    final endMonth = quarter * 3;
    return DateTime(date.year, endMonth + 1, 0);
  }
  
  // Check if year is leap year
  static bool isLeapYear(int year) {
    return (year % 4 == 0 && year % 100 != 0) || (year % 400 == 0);
  }
  
  // Get days in month
  static int getDaysInMonth(int year, int month) {
    return DateTime(year, month + 1, 0).day;
  }
  
  // Get week number of year
  static int getWeekOfYear(DateTime date) {
    final firstDayOfYear = DateTime(date.year, 1, 1);
    final days = date.difference(firstDayOfYear).inDays;
    return ((days + firstDayOfYear.weekday) / 7).ceil();
  }
  
  // Get dates for a specific weekday in a month
  static List<DateTime> getWeekdaysInMonth(int year, int month, int weekday) {
    final dates = <DateTime>[];
    final firstDay = DateTime(year, month, 1);
    final lastDay = DateTime(year, month + 1, 0);
    
    // Find first occurrence of weekday
    int firstOccurrence = firstDay.weekday <= weekday 
        ? weekday - firstDay.weekday + 1
        : 7 - firstDay.weekday + weekday + 1;
    
    // Add all occurrences
    for (int day = firstOccurrence; day <= lastDay.day; day += 7) {
      dates.add(DateTime(year, month, day));
    }
    
    return dates;
  }
  
  // Check if two dates are on the same day
  static bool isSameDay(DateTime date1, DateTime date2) {
    return date1.year == date2.year &&
           date1.month == date2.month &&
           date1.day == date2.day;
  }
  
  // Check if two dates are in the same week
  static bool isSameWeek(DateTime date1, DateTime date2) {
    final startOfWeek1 = date1.subtract(Duration(days: date1.weekday - 1));
    final startOfWeek2 = date2.subtract(Duration(days: date2.weekday - 1));
    return isSameDay(startOfWeek1, startOfWeek2);
  }
  
  // Check if two dates are in the same month
  static bool isSameMonth(DateTime date1, DateTime date2) {
    return date1.year == date2.year && date1.month == date2.month;
  }
  
  // Check if two dates are in the same year
  static bool isSameYear(DateTime date1, DateTime date2) {
    return date1.year == date2.year;
  }
  
  // Get relative date ranges for filtering
  static DateRange getLastDays(int days) {
    final end = today;
    final start = end.subtract(Duration(days: days - 1));
    return DateRange(start, end);
  }
  
  static DateRange getLastWeeks(int weeks) {
    final end = today;
    final start = end.subtract(Duration(days: weeks * 7 - 1));
    return DateRange(start, end);
  }
  
  static DateRange getLastMonths(int months) {
    final end = today;
    final start = DateTime(end.year, end.month - months + 1, end.day);
    return DateRange(start, end);
  }
  
  // Common date ranges
  static DateRange get thisWeek => DateRange(startOfWeek, endOfWeek);
  static DateRange get thisMonth => DateRange(startOfMonth, endOfMonth);
  static DateRange get thisYear => DateRange(startOfYear, endOfYear);
  static DateRange get last7Days => getLastDays(7);
  static DateRange get last30Days => getLastDays(30);
  static DateRange get last90Days => getLastDays(90);
}

// Date range helper class
class DateRange {
  final DateTime start;
  final DateTime end;
  
  const DateRange(this.start, this.end);
  
  bool contains(DateTime date) {
    return DateHelpers.isInRange(date, start, end);
  }
  
  Duration get duration => end.difference(start);
  
  int get days => duration.inDays + 1;
  
  List<DateTime> getDates() {
    final dates = <DateTime>[];
    DateTime current = start;
    
    while (current.isBefore(end) || current.isAtSameMomentAs(end)) {
      dates.add(current);
      current = current.add(const Duration(days: 1));
    }
    
    return dates;
  }
  
  @override
  String toString() => '$start - $end';
  
  @override
  bool operator ==(Object other) {
    return other is DateRange && 
           other.start == start && 
           other.end == end;
  }
  
  @override
  int get hashCode => start.hashCode ^ end.hashCode;
}