import '../extensions/string_extensions.dart';
import '../extensions/date_extensions.dart';

class Formatters {
  // Currency formatting
  static String currency(double amount, {String symbol = 'TSh', int decimals = 0}) {
    final formatted = amount.toStringAsFixed(decimals);
    final parts = formatted.split('.');
    final integerPart = parts[0];
    final decimalPart = parts.length > 1 ? parts[1] : '';
    
    // Add thousand separators
    final regex = RegExp(r'(\d{1,3})(?=(\d{3})+(?!\d))');
    final formattedInteger = integerPart.replaceAllMapped(regex, (match) => '${match[1]},');
    
    if (decimals > 0 && decimalPart.isNotEmpty) {
      return '$symbol $formattedInteger.$decimalPart';
    }
    return '$symbol $formattedInteger';
  }
  
  // Phone number formatting
  static String phoneNumber(String phone) {
    return phone.formatPhoneNumber();
  }
  
  // Distance formatting
  static String distance(double distanceInKm) {
    if (distanceInKm < 1) {
      final meters = (distanceInKm * 1000).round();
      return '${meters}m';
    } else if (distanceInKm < 10) {
      return '${distanceInKm.toStringAsFixed(1)}km';
    } else {
      return '${distanceInKm.round()}km';
    }
  }
  
  // Area formatting
  static String area(double areaInSqM) {
    if (areaInSqM < 1000) {
      return '${areaInSqM.round()}m²';
    } else {
      final areaInSqKm = areaInSqM / 1000000;
      return '${areaInSqKm.toStringAsFixed(2)}km²';
    }
  }
  
  // File size formatting
  static String fileSize(int bytes) {
    const suffixes = ['B', 'KB', 'MB', 'GB', 'TB'];
    var i = 0;
    double size = bytes.toDouble();
    
    while (size >= 1024 && i < suffixes.length - 1) {
      size /= 1024;
      i++;
    }
    
    return '${size.toStringAsFixed(i == 0 ? 0 : 1)}${suffixes[i]}';
  }
  
  // Percentage formatting
  static String percentage(double value, {int decimals = 1}) {
    return '${(value * 100).toStringAsFixed(decimals)}%';
  }
  
  // Rating formatting
  static String rating(double rating, {int decimals = 1}) {
    return rating.toStringAsFixed(decimals);
  }
  
  // Duration formatting
  static String duration(Duration duration) {
    final hours = duration.inHours;
    final minutes = duration.inMinutes.remainder(60);
    
    if (hours > 0) {
      return '${hours}h ${minutes}m';
    } else {
      return '${minutes}m';
    }
  }
  
  // Time ago formatting
  static String timeAgo(DateTime dateTime) {
    return dateTime.timeAgo;
  }
  
  // Date formatting for different contexts
  static String bookingDate(DateTime dateTime) {
    return dateTime.bookingDateFormat;
  }
  
  static String chatDate(DateTime dateTime) {
    return dateTime.chatDateFormat;
  }
  
  static String reviewDate(DateTime dateTime) {
    return dateTime.reviewDateFormat;
  }
  
  // Number formatting with abbreviations
  static String compactNumber(int number) {
    if (number < 1000) {
      return number.toString();
    } else if (number < 1000000) {
      final k = number / 1000;
      return k % 1 == 0 ? '${k.toInt()}K' : '${k.toStringAsFixed(1)}K';
    } else {
      final m = number / 1000000;
      return m % 1 == 0 ? '${m.toInt()}M' : '${m.toStringAsFixed(1)}M';
    }
  }
  
  // Name initials
  static String initials(String name) {
    final parts = name.trim().split(' ');
    if (parts.isEmpty) return '';
    
    if (parts.length == 1) {
      return parts[0].isNotEmpty ? parts[0][0].toUpperCase() : '';
    }
    
    final first = parts.first.isNotEmpty ? parts.first[0] : '';
    final last = parts.last.isNotEmpty ? parts.last[0] : '';
    
    return (first + last).toUpperCase();
  }
  
  // Truncate text with ellipsis
  static String truncate(String text, int maxLength, {String suffix = '...'}) {
    return text.truncate(maxLength, suffix);
  }
  
  // Format search query
  static String searchQuery(String query) {
    return query.trim().toLowerCase();
  }
  
  // Format address for display
  static String address(String address, {int maxLines = 2}) {
    final words = address.split(' ');
    if (words.length <= 6) return address;
    
    // Split into lines
    final firstLine = words.take(3).join(' ');
    final secondLine = words.skip(3).take(3).join(' ');
    final remaining = words.skip(6).join(' ');
    
    if (maxLines == 1) {
      return remaining.isNotEmpty ? '$firstLine...' : firstLine;
    } else if (maxLines == 2) {
      if (remaining.isNotEmpty) {
        return '$firstLine\n$secondLine...';
      } else {
        return '$firstLine\n$secondLine';
      }
    }
    
    return address;
  }
  
  // Format price range
  static String priceRange(double minPrice, double maxPrice, {String symbol = 'TSh'}) {
    if (minPrice == maxPrice) {
      return currency(minPrice, symbol: symbol);
    }
    return '${currency(minPrice, symbol: symbol)} - ${currency(maxPrice, symbol: symbol)}';
  }
  
  // Format house features list
  static String houseFeatures(List<String> features, {int maxItems = 3}) {
    if (features.isEmpty) return 'No features listed';
    
    if (features.length <= maxItems) {
      return features.join(', ');
    }
    
    final visible = features.take(maxItems).join(', ');
    final remaining = features.length - maxItems;
    return '$visible +$remaining more';
  }
  
  // Format review summary
  static String reviewSummary(double averageRating, int totalReviews) {
    if (totalReviews == 0) return 'No reviews yet';
    
    final ratingText = rating(averageRating);
    final reviewText = totalReviews == 1 ? 'review' : 'reviews';
    return '$ratingText (${compactNumber(totalReviews)} $reviewText)';
  }
  
  // Format booking status
  static String bookingStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Pending Approval';
      case 'confirmed':
        return 'Confirmed';
      case 'cancelled':
        return 'Cancelled';
      case 'completed':
        return 'Completed';
      case 'in_progress':
        return 'In Progress';
      default:
        return status.capitalizeWords;
    }
  }
  
  // Format payment status
  static String paymentStatus(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return 'Payment Pending';
      case 'processing':
        return 'Processing Payment';
      case 'completed':
        return 'Payment Successful';
      case 'failed':
        return 'Payment Failed';
      case 'refunded':
        return 'Refunded';
      default:
        return status.capitalizeWords;
    }
  }
  
  // Format user role
  static String userRole(String role) {
    switch (role.toLowerCase()) {
      case 'customer':
        return 'Customer';
      case 'household':
        return 'Property Owner';
      case 'admin':
        return 'Administrator';
      default:
        return role.capitalizeWords;
    }
  }
}