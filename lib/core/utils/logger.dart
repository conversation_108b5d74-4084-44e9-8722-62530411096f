import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

enum LogLevel {
  debug,
  info,
  warning,
  error,
}

class Logger {
  static const String _name = '<PERSON><PERSON><PERSON><PERSON>';
  static bool _enabled = kDebugMode;
  static LogLevel _minLevel = LogLevel.debug;

  static void setEnabled(bool enabled) {
    _enabled = enabled;
  }

  static void setMinLevel(LogLevel level) {
    _minLevel = level;
  }

  static void debug(String message, [Object? error, StackTrace? stackTrace]) {
    _log(LogLevel.debug, message, error, stackTrace);
  }

  static void info(String message, [Object? error, StackTrace? stackTrace]) {
    _log(LogLevel.info, message, error, stackTrace);
  }

  static void warning(String message, [Object? error, StackTrace? stackTrace]) {
    _log(LogLevel.warning, message, error, stackTrace);
  }

  static void error(String message, [Object? error, StackTrace? stackTrace]) {
    _log(LogLevel.error, message, error, stackTrace);
  }

  static void _log(
    LogLevel level,
    String message, [
    Object? error,
    StackTrace? stackTrace,
  ]) {
    if (!_enabled || level.index < _minLevel.index) return;

    final now = DateTime.now();

    if (kDebugMode) {
      // Use developer.log for better formatting in debug console
      developer.log(
        message,
        time: now,
        level: _getLevelValue(level),
        name: _name,
        error: error,
        stackTrace: stackTrace,
      );
    } else {
      // In release mode, we could send logs to a remote service
      // For now, we'll just ignore them to avoid print statements in production
      // TODO: Implement remote logging service for production
    }
  }

  static int _getLevelValue(LogLevel level) {
    switch (level) {
      case LogLevel.debug:
        return 500;
      case LogLevel.info:
        return 800;
      case LogLevel.warning:
        return 900;
      case LogLevel.error:
        return 1000;
    }
  }

  // Network logging helpers
  static void logRequest(String method, String url, {Map<String, dynamic>? data}) {
    info('🌐 $method $url${data != null ? '\nData: $data' : ''}');
  }

  static void logResponse(int statusCode, String url, {dynamic data}) {
    if (statusCode >= 200 && statusCode < 300) {
      info('✅ $statusCode $url${data != null ? '\nResponse: $data' : ''}');
    } else {
      warning('⚠️ $statusCode $url${data != null ? '\nResponse: $data' : ''}');
    }
  }

  static void logError(String context, Object error, [StackTrace? stackTrace]) {
    Logger.error('❌ $context: $error', error, stackTrace);
  }

  // User action logging
  static void logUserAction(String action, [Map<String, dynamic>? details]) {
    info('👤 User Action: $action${details != null ? '\nDetails: $details' : ''}');
  }

  // Navigation logging
  static void logNavigation(String from, String to) {
    info('📍 Navigation: $from → $to');
  }

  // Performance logging
  static void logPerformance(String operation, Duration duration) {
    info('⏱️ Performance: $operation took ${duration.inMilliseconds}ms');
  }

  // Database logging
  static void logDatabase(String operation, [String? table, Map<String, dynamic>? data]) {
    info('🗄️ Database: $operation${table != null ? ' on $table' : ''}${data != null ? '\nData: $data' : ''}');
  }

  // Auth logging
  static void logAuth(String operation, [String? userId]) {
    info('🔐 Auth: $operation${userId != null ? ' for user $userId' : ''}');
  }

  // Feature flag logging
  static void logFeature(String feature, bool enabled) {
    info('🚩 Feature: $feature is ${enabled ? 'enabled' : 'disabled'}');
  }
}