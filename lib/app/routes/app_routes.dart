import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../core/services/session_service.dart';
import '../../core/services/storage_service.dart';
import '../../core/utils/logger.dart';
import '../../features/home/<USER>/home_routes.dart';

class AppRoutes {
  // Route paths
  static const String splash = '/';
  static const String onboarding = '/onboarding';
  static const String login = '/login';
  static const String register = '/register';
  static const String customerHome = '/customer-home';
  static const String landlordHome = '/landlord-home';
  static const String search = '/search';
  static const String profile = '/profile';
  static const String settings = '/settings';
  static const String notifications = '/notifications';
  
  // GoRouter configuration
  static final GoRouter router = GoRouter(
    initialLocation: splash,
    debugLogDiagnostics: true,
    redirect: _redirect,
    routes: [
      // Splash route
      GoRoute(
        path: splash,
        name: 'splash',
        builder: (context, state) => const SplashScreen(),
      ),
      
      // Onboarding route
      GoRoute(
        path: onboarding,
        name: 'onboarding',
        builder: (context, state) => const OnboardingScreen(),
      ),
      
      // Authentication routes
      GoRoute(
        path: login,
        name: 'login',
        builder: (context, state) => const LoginScreen(),
      ),
      
      GoRoute(
        path: register,
        name: 'register',
        builder: (context, state) => const RegisterScreen(),
      ),
      
      // Home routes (require authentication)
      ...HomeRoutes.routes,

      // TODO: Add other feature routes here when they are implemented
      // - Search routes
      // - Profile routes
      // - Settings routes
      // - Notifications routes
    ],
    errorBuilder: (context, state) => ErrorScreen(
      error: state.error.toString(),
    ),
  );
  
  // Route redirect logic
  static String? _redirect(BuildContext context, GoRouterState state) {
    final sessionService = SessionService.instance;
    final isLoggedIn = sessionService.isLoggedIn;
    final isOnboardingComplete = StorageService.instance.isOnboardingComplete();
    final currentPath = state.uri.toString();

    Logger.info('Routing: Current path: $currentPath, Logged in: $isLoggedIn, Onboarding: $isOnboardingComplete');

    // Don't redirect on splash screen
    if (currentPath == splash) {
      return null;
    }

    // If onboarding not complete, redirect to onboarding
    if (!isOnboardingComplete && currentPath != onboarding) {
      return onboarding;
    }

    // If not logged in, redirect to customer home (allow unauthenticated access)
    if (!isLoggedIn && !_isAuthRoute(currentPath) && currentPath != customerHome) {
      return customerHome;
    }

    // If logged in and trying to access auth pages, redirect based on user role
    if (isLoggedIn && _isAuthRoute(currentPath)) {
      final user = sessionService.currentUser;
      if (user?.role.name == 'landlord') {
        return landlordHome;
      } else {
        return customerHome;
      }
    }

    return null;
  }
  
  // Check if the route is an authentication route
  static bool _isAuthRoute(String path) {
    return path == login || path == register || path == onboarding;
  }
  
  // Navigation helpers
  static void goToSplash(BuildContext context) {
    context.go(splash);
  }
  
  static void goToOnboarding(BuildContext context) {
    context.go(onboarding);
  }
  
  static void goToLogin(BuildContext context) {
    context.go(login);
  }
  
  static void goToRegister(BuildContext context) {
    context.go(register);
  }
  
  static void goToCustomerHome(BuildContext context) {
    context.go(customerHome);
  }

  static void goToLandlordHome(BuildContext context) {
    context.go(landlordHome);
  }
  
  // TODO: Add navigation methods for other features when implemented
  // static void goToSearch(BuildContext context) { context.go(search); }
  // static void goToProfile(BuildContext context) { context.go(profile); }
  // static void goToSettings(BuildContext context) { context.go(settings); }
  // static void goToNotifications(BuildContext context) { context.go(notifications); }
}

// Temporary placeholder screens
class SplashScreen extends StatefulWidget {
  const SplashScreen({super.key});

  @override
  State<SplashScreen> createState() => _SplashScreenState();
}

class _SplashScreenState extends State<SplashScreen> {
  @override
  void initState() {
    super.initState();
    _initializeApp();
  }

  Future<void> _initializeApp() async {
    // Wait for splash animation
    await Future.delayed(const Duration(seconds: 2));
    
    if (mounted) {
      // Check if onboarding is complete
      final isOnboardingComplete = StorageService.instance.isOnboardingComplete();
      final isLoggedIn = SessionService.instance.isLoggedIn;
      
      if (!isOnboardingComplete) {
        AppRoutes.goToOnboarding(context);
      } else if (!isLoggedIn) {
        AppRoutes.goToLogin(context);
      } else {
        AppRoutes.goToCustomerHome(context);
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Container(
        width: double.infinity,
        height: double.infinity,
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Theme.of(context).primaryColor,
              Theme.of(context).primaryColor.withValues(alpha: 0.8),
            ],
          ),
        ),
        child: const Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.home_work,
              size: 100,
              color: Colors.white,
            ),
            SizedBox(height: 20),
            Text(
              'EMakazi',
              style: TextStyle(
                fontSize: 32,
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            SizedBox(height: 10),
            Text(
              'Find Your Perfect Home',
              style: TextStyle(
                fontSize: 16,
                color: Colors.white70,
              ),
            ),
            SizedBox(height: 50),
            CircularProgressIndicator(
              valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
            ),
          ],
        ),
      ),
    );
  }
}

class OnboardingScreen extends StatelessWidget {
  const OnboardingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text(
              'Welcome to EMakazi',
              style: TextStyle(fontSize: 24, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 20),
            const Text(
              'Your home rental platform',
              style: TextStyle(fontSize: 16),
            ),
            const SizedBox(height: 40),
            ElevatedButton(
              onPressed: () {
                StorageService.instance.setOnboardingComplete(true);
                AppRoutes.goToLogin(context);
              },
              child: const Text('Get Started'),
            ),
          ],
        ),
      ),
    );
  }
}

class LoginScreen extends StatelessWidget {
  const LoginScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Login')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('Login Screen - TODO: Implement'),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                // TODO: Implement actual login
                SessionService.instance.loginWithCredentials('<EMAIL>', 'password');
                AppRoutes.goToCustomerHome(context);
              },
              child: const Text('Login (Demo)'),
            ),
            TextButton(
              onPressed: () => AppRoutes.goToRegister(context),
              child: const Text('Register'),
            ),
          ],
        ),
      ),
    );
  }
}

class RegisterScreen extends StatelessWidget {
  const RegisterScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(title: const Text('Register')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Text('Register Screen - TODO: Implement'),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () {
                // TODO: Implement actual registration
                AppRoutes.goToLogin(context);
              },
              child: const Text('Register'),
            ),
            TextButton(
              onPressed: () => AppRoutes.goToLogin(context),
              child: const Text('Login'),
            ),
          ],
        ),
      ),
    );
  }
}

// TODO: Move these screens to their respective feature folders

class ErrorScreen extends StatelessWidget {
  final String error;

  const ErrorScreen({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 20),
            const Text('Page Not Found', style: TextStyle(fontSize: 24)),
            const SizedBox(height: 10),
            Text(error),
            const SizedBox(height: 20),
            ElevatedButton(
              onPressed: () => AppRoutes.goToCustomerHome(context),
              child: const Text('Go Home'),
            ),
          ],
        ),
      ),
    );
  }
}
