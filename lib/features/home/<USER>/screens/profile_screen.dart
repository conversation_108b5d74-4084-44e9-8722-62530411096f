import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../../../../app/theme/app_colors.dart';
import '../../../../app/theme/app_text_styles.dart';
import '../../../authentication/presentation/widgets/auth_button.dart';

class ProfileScreen extends StatelessWidget {
  final bool isAuthenticated;

  const ProfileScreen({
    super.key,
    required this.isAuthenticated,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppColors.background,
      appBar: AppBar(
        backgroundColor: AppColors.background,
        elevation: 0,
        title: Text(
          'Profile',
          style: AppTextStyles.headlineSmall.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: isAuthenticated ? _buildAuthenticatedView() : _buildUnauthenticatedView(context),
    );
  }

  Widget _buildUnauthenticatedView(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Login Section
          _buildLoginSection(context),
          const SizedBox(height: 32),

          // Divider
          const Divider(color: AppColors.border),
          const SizedBox(height: 24),

          // Account Settings Section
          _buildAccountSettingsSection(context),
          const SizedBox(height: 24),

          // Divider
          const Divider(color: AppColors.border),
          const SizedBox(height: 24),

          // Legal Section
          _buildLegalSection(context),
        ],
      ),
    );
  }

  Widget _buildLoginSection(BuildContext context) {
    return Column(
      children: [
        // Profile Icon
        Container(
          width: 100,
          height: 100,
          decoration: BoxDecoration(
            color: AppColors.primary.withValues(alpha: 0.1),
            shape: BoxShape.circle,
          ),
          child: Icon(
            Icons.person_outline,
            size: 50,
            color: AppColors.primary,
          ),
        ),
        const SizedBox(height: 24),

        // Title
        Text(
          'Login and start planning your Accommodation',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
          textAlign: TextAlign.center,
        ),
        const SizedBox(height: 24),

        // Login Button
        AuthButton(
          text: 'Log In',
          onPressed: () {
            context.go('/login');
          },
          backgroundColor: AppColors.primary,
          width: double.infinity,
          icon: Icons.login,
        ),
      ],
    );
  }

  Widget _buildAccountSettingsSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Account Settings',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        _buildSettingsItem(
          context,
          icon: Icons.info_outline,
          title: 'About',
          onTap: () {
            context.go('/account-settings/about');
          },
        ),
        _buildSettingsItem(
          context,
          icon: Icons.settings,
          title: 'Advanced Settings',
          onTap: () {
            context.go('/account-settings/advanced');
          },
        ),
        _buildSettingsItem(
          context,
          icon: Icons.feedback_outlined,
          title: 'Send Feedback',
          onTap: () {
            context.go('/account-settings/feedback');
          },
        ),
      ],
    );
  }

  Widget _buildLegalSection(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Legal',
          style: AppTextStyles.bodyLarge.copyWith(
            color: AppColors.textPrimary,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 16),
        _buildSettingsItem(
          context,
          icon: Icons.privacy_tip_outlined,
          title: 'Privacy Policy',
          onTap: () {
            context.go('/legal/privacy');
          },
        ),
        _buildSettingsItem(
          context,
          icon: Icons.description_outlined,
          title: 'Terms of Service',
          onTap: () {
            context.go('/legal/terms');
          },
        ),
      ],
    );
  }

  Widget _buildSettingsItem(
    BuildContext context, {
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(12),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
        child: Row(
          children: [
            Icon(
              icon,
              color: AppColors.textSecondary,
              size: 24,
            ),
            const SizedBox(width: 16),
            Expanded(
              child: Text(
                title,
                style: AppTextStyles.bodyMedium.copyWith(
                  color: AppColors.textPrimary,
                ),
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: AppColors.textSecondary,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAuthenticatedView() {
    // TODO: Implement authenticated profile view
    return const Center(
      child: Text('Authenticated Profile View - To be implemented'),
    );
  }
}
