extension DateTimeExtensions on DateTime {
  // Common date formats
  String get toDateString => '${day.toString().padLeft(2, '0')}/${month.toString().padLeft(2, '0')}/$year';
  
  String get toTimeString => '${hour.toString().padLeft(2, '0')}:${minute.toString().padLeft(2, '0')}';
  
  String get toDateTimeString => '$toDateString $toTimeString';
  
  String get toIsoDateString => toIso8601String().split('T').first;
  
  // Relative time
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(this);
    
    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return years == 1 ? '1 year ago' : '$years years ago';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? '1 month ago' : '$months months ago';
    } else if (difference.inDays > 0) {
      return difference.inDays == 1 ? '1 day ago' : '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return difference.inHours == 1 ? '1 hour ago' : '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return difference.inMinutes == 1 ? '1 minute ago' : '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }
  
  String get timeUntil {
    final difference = this.difference(DateTime.now());
    
    if (difference.isNegative) return 'Past due';
    
    if (difference.inDays > 365) {
      final years = (difference.inDays / 365).floor();
      return years == 1 ? 'In 1 year' : 'In $years years';
    } else if (difference.inDays > 30) {
      final months = (difference.inDays / 30).floor();
      return months == 1 ? 'In 1 month' : 'In $months months';
    } else if (difference.inDays > 0) {
      return difference.inDays == 1 ? 'Tomorrow' : 'In ${difference.inDays} days';
    } else if (difference.inHours > 0) {
      return difference.inHours == 1 ? 'In 1 hour' : 'In ${difference.inHours} hours';
    } else if (difference.inMinutes > 0) {
      return difference.inMinutes == 1 ? 'In 1 minute' : 'In ${difference.inMinutes} minutes';
    } else {
      return 'Now';
    }
  }
  
  // Day helpers
  bool get isToday {
    final now = DateTime.now();
    return year == now.year && month == now.month && day == now.day;
  }
  
  bool get isYesterday {
    final yesterday = DateTime.now().subtract(const Duration(days: 1));
    return year == yesterday.year && month == yesterday.month && day == yesterday.day;
  }
  
  bool get isTomorrow {
    final tomorrow = DateTime.now().add(const Duration(days: 1));
    return year == tomorrow.year && month == tomorrow.month && day == tomorrow.day;
  }
  
  bool get isThisWeek {
    final now = DateTime.now();
    final startOfWeek = now.subtract(Duration(days: now.weekday - 1));
    final endOfWeek = startOfWeek.add(const Duration(days: 6));
    return isAfter(startOfWeek.subtract(const Duration(days: 1))) && 
           isBefore(endOfWeek.add(const Duration(days: 1)));
  }
  
  bool get isThisMonth {
    final now = DateTime.now();
    return year == now.year && month == now.month;
  }
  
  bool get isThisYear {
    return year == DateTime.now().year;
  }
  
  bool get isPast => isBefore(DateTime.now());
  
  bool get isFuture => isAfter(DateTime.now());
  
  // Week helpers
  DateTime get startOfWeek {
    return subtract(Duration(days: weekday - 1));
  }
  
  DateTime get endOfWeek {
    return add(Duration(days: 7 - weekday));
  }
  
  DateTime get startOfMonth {
    return DateTime(year, month, 1);
  }
  
  DateTime get endOfMonth {
    return DateTime(year, month + 1, 0);
  }
  
  DateTime get startOfYear {
    return DateTime(year, 1, 1);
  }
  
  DateTime get endOfYear {
    return DateTime(year, 12, 31);
  }
  
  // Date only (without time)
  DateTime get dateOnly {
    return DateTime(year, month, day);
  }
  
  // Business days
  bool get isWeekend => weekday == DateTime.saturday || weekday == DateTime.sunday;
  
  bool get isWeekday => !isWeekend;
  
  // Age calculation
  int ageFrom(DateTime birthDate) {
    int age = year - birthDate.year;
    if (month < birthDate.month || (month == birthDate.month && day < birthDate.day)) {
      age--;
    }
    return age;
  }
  
  // Time zone helpers
  DateTime get localTime => toLocal();
  
  DateTime get utcTime => toUtc();
  
  // Quarter helpers
  int get quarter {
    return ((month - 1) / 3).floor() + 1;
  }
  
  DateTime get startOfQuarter {
    final quarterStartMonth = ((quarter - 1) * 3) + 1;
    return DateTime(year, quarterStartMonth, 1);
  }
  
  DateTime get endOfQuarter {
    final quarterEndMonth = quarter * 3;
    return DateTime(year, quarterEndMonth + 1, 0);
  }
  
  // Day name helpers
  String get dayName {
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];
    return days[weekday - 1];
  }
  
  String get shortDayName {
    const days = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
    return days[weekday - 1];
  }
  
  String get monthName {
    const months = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];
    return months[month - 1];
  }
  
  String get shortMonthName {
    const months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return months[month - 1];
  }
  
  // Comparison helpers
  bool isSameDay(DateTime other) {
    return year == other.year && month == other.month && day == other.day;
  }
  
  bool isSameWeek(DateTime other) {
    final thisWeekStart = startOfWeek;
    final otherWeekStart = other.startOfWeek;
    return thisWeekStart.isSameDay(otherWeekStart);
  }
  
  bool isSameMonth(DateTime other) {
    return year == other.year && month == other.month;
  }
  
  bool isSameYear(DateTime other) {
    return year == other.year;
  }
  
  // Date arithmetic
  DateTime addBusinessDays(int days) {
    DateTime result = this;
    int remainingDays = days;
    
    while (remainingDays > 0) {
      result = result.add(const Duration(days: 1));
      if (result.isWeekday) {
        remainingDays--;
      }
    }
    
    return result;
  }
  
  DateTime subtractBusinessDays(int days) {
    DateTime result = this;
    int remainingDays = days;
    
    while (remainingDays > 0) {
      result = result.subtract(const Duration(days: 1));
      if (result.isWeekday) {
        remainingDays--;
      }
    }
    
    return result;
  }
  
  // Formatting helpers for specific use cases
  String get bookingDateFormat {
    if (isToday) return 'Today, $toTimeString';
    if (isTomorrow) return 'Tomorrow, $toTimeString';
    if (isYesterday) return 'Yesterday, $toTimeString';
    return '$shortDayName $day $shortMonthName, $toTimeString';
  }
  
  String get chatDateFormat {
    if (isToday) return toTimeString;
    if (isYesterday) return 'Yesterday';
    if (isThisWeek) return shortDayName;
    if (isThisYear) return '$day $shortMonthName';
    return '$day/${month.toString().padLeft(2, '0')}/$year';
  }
  
  String get reviewDateFormat {
    if (isToday) return 'Today';
    if (isYesterday) return 'Yesterday';
    if (isThisWeek) return dayName;
    if (isThisMonth) return '$dayName $day';
    if (isThisYear) return '$day $monthName';
    return '$day $monthName $year';
  }
  
  // Duration helpers
  Duration get sinceNow => DateTime.now().difference(this);
  
  Duration get untilNow => difference(DateTime.now());
  
  // Validation helpers
  bool get isValidBirthDate {
    final now = DateTime.now();
    final minDate = DateTime(now.year - 120, now.month, now.day);
    return isAfter(minDate) && isBefore(now);
  }
  
  bool get isValidFutureDate {
    return isAfter(DateTime.now());
  }
  
  bool get isValidPastDate {
    return isBefore(DateTime.now());
  }
}