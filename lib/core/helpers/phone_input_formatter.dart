import 'package:flutter/services.dart';

/// Custom input formatter for Tanzania phone numbers
/// Automatically formats input to +255 7XX XXX XXX or +255 6XX XXX XXX
class TanzaniaPhoneInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Get the new text without any formatting
    String newText = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
    
    // If user is deleting, allow it
    if (newText.length < oldValue.text.replaceAll(RegExp(r'[^\d]'), '').length) {
      return _formatPhoneNumber(newText, newValue.selection);
    }
    
    // Limit to maximum digits (9 digits after +255)
    if (newText.length > 9) {
      newText = newText.substring(0, 9);
    }
    
    return _formatPhoneNumber(newText, newValue.selection);
  }
  
  TextEditingValue _formatPhoneNumber(String digits, TextSelection selection) {
    String formatted = '+255 ';
    int cursorPosition = formatted.length;
    
    if (digits.isNotEmpty) {
      // First digit must be 6 or 7
      if (digits.length >= 1) {
        String firstDigit = digits[0];
        if (firstDigit != '6' && firstDigit != '7') {
          // If first digit is not 6 or 7, default to 7
          firstDigit = '7';
          digits = firstDigit + (digits.length > 1 ? digits.substring(1) : '');
        }
        formatted += firstDigit;
        cursorPosition = formatted.length;
        
        // Add remaining digits with formatting
        if (digits.length > 1) {
          String remaining = digits.substring(1);
          
          // Add first group (2 digits)
          if (remaining.length >= 1) {
            formatted += remaining.substring(0, remaining.length >= 2 ? 2 : 1);
            if (remaining.length >= 2) {
              formatted += ' ';
            }
            cursorPosition = formatted.length;
          }
          
          // Add second group (3 digits)
          if (remaining.length >= 3) {
            formatted += remaining.substring(2, remaining.length >= 5 ? 5 : remaining.length);
            if (remaining.length >= 5) {
              formatted += ' ';
            }
            cursorPosition = formatted.length;
          }
          
          // Add third group (3 digits)
          if (remaining.length >= 6) {
            formatted += remaining.substring(5);
            cursorPosition = formatted.length;
          }
        }
      }
    }
    
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: cursorPosition),
    );
  }
}

/// Input formatter that only allows digits and limits length
class TanzaniaPhoneDigitsFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Only allow digits
    String newText = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
    
    // Limit to 9 digits (after +255)
    if (newText.length > 9) {
      newText = newText.substring(0, 9);
    }
    
    // Ensure first digit is 6 or 7
    if (newText.isNotEmpty && newText[0] != '6' && newText[0] != '7') {
      if (oldValue.text.isNotEmpty) {
        // Keep old value if new first digit is invalid
        return oldValue;
      } else {
        // Default to 7 if starting fresh
        newText = '7' + (newText.length > 1 ? newText.substring(1) : '');
      }
    }
    
    return TextEditingValue(
      text: newText,
      selection: TextSelection.collapsed(offset: newText.length),
    );
  }
}
