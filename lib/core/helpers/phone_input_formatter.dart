import 'package:flutter/services.dart';

/// Simple phone input formatter for Tanzania phone numbers
/// Formats input to +255 7XX XXX XXX or +255 6XX XXX XXX
class TanzaniaPhoneInputFormatter extends TextInputFormatter {
  @override
  TextEditingValue formatEditUpdate(
    TextEditingValue oldValue,
    TextEditingValue newValue,
  ) {
    // Extract only digits from the new input
    String digits = newValue.text.replaceAll(RegExp(r'[^\d]'), '');
    
    // Limit to 9 digits maximum
    if (digits.length > 9) {
      digits = digits.substring(0, 9);
    }
    
    // Only validate the first digit - it must be 6 or 7
    if (digits.isNotEmpty && digits.length == 1 && digits[0] != '6' && digits[0] != '7') {
      return oldValue;
    }

    // If we already have a valid first digit, make sure it stays valid
    if (digits.length > 1 && digits[0] != '6' && digits[0] != '7') {
      return oldValue;
    }
    
    // Format the phone number
    String formatted = '+255 ';
    
    if (digits.isNotEmpty) {
      formatted += digits[0]; // First digit (6 or 7)
      
      if (digits.length > 1) {
        formatted += digits.substring(1, digits.length > 3 ? 3 : digits.length);
        
        if (digits.length > 3) {
          formatted += ' ${digits.substring(3, digits.length > 6 ? 6 : digits.length)}';
          
          if (digits.length > 6) {
            formatted += ' ${digits.substring(6)}';
          }
        }
      }
    }
    
    return TextEditingValue(
      text: formatted,
      selection: TextSelection.collapsed(offset: formatted.length),
    );
  }
}
